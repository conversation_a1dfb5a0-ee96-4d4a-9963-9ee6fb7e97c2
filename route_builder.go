package tiktokservice

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
)

const (
	BaseAPIURL        = "business-api.tiktok.com"
	BaseSandboxAPIURL = "sandbox-ads.tiktok.com"
)

// RouteBuilder helps building facebook API request routes.
type RouteBuilder struct {
	err     error
	version string
	path    string
	v       url.Values
}

// Filter is used for filtering lists.
type Filter struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// NewRoute starts building a new route.
func NewRoute(version, format string, a ...interface{}) *RouteBuilder {
	return &RouteBuilder{
		version: version,
		v:       url.Values{},
		path:    fmt.Sprintf(format, a...),
	}
}

// Fields sets the fields query param.
func (rb *RouteBuilder) Fields(f ...string) *RouteBuilder {
	if len(f) > 0 {
		rb.v.Set("fields", strings.Join(f, ","))
	} else {
		rb.v.Del("fields")
	}

	return rb
}

// SetJSONArray sets a parameter as a JSON array.
func (rb *RouteBuilder) SetJSONArray(param string, values []string) *RouteBuilder {
	if len(values) > 0 {
		b, err := json.Marshal(values)
		if err != nil {
			rb.err = err
			return rb
		}
		rb.v.Set(param, string(b))
	} else {
		rb.v.Del(param)
	}
	return rb
}

// Redirect parameter
func (rb *RouteBuilder) SetParam(param string, value string) *RouteBuilder {
	if value != "" {
		rb.v.Set(param, value)
	} else {
		rb.v.Del(param)
	}

	return rb
}

func (rb *RouteBuilder) SetParams(params map[string]string) *RouteBuilder {
	if len(params) > 0 {
		for k, v := range params {
			if k == "" {
				continue
			}
			if v != "" {
				rb.v.Set(k, v)
			} else {
				rb.v.Del(k)
			}
		}
	}

	return rb
}

// Limit sets the page_size param.
func (rb *RouteBuilder) PageSize(limit int) *RouteBuilder {
	if limit > -1 {
		rb.v.Set("page_size", strconv.Itoa(limit))
	} else {
		rb.v.Del("page_size")
	}

	return rb
}

// BeforeCursor sets the before cursor param for pagination.
func (rb *RouteBuilder) BeforeCursor(cursor string) *RouteBuilder {
	if cursor != "" {
		rb.v.Set("before", cursor)
	} else {
		rb.v.Del("before")
	}

	return rb
}

// AfterCursor sets the after cursor param for pagination.
func (rb *RouteBuilder) AfterCursor(cursor string) *RouteBuilder {
	if cursor != "" {
		rb.v.Set("after", cursor)
	} else {
		rb.v.Del("after")
	}

	return rb
}

// Filtering sets filtering param or deletes it.
func (rb *RouteBuilder) Filtering(f ...Filter) *RouteBuilder {
	if len(f) > 0 {
		b, err := json.Marshal(f)
		if err != nil {
			rb.err = err
		}

		rb.v.Set("filtering", string(b))
	} else {
		rb.v.Del("filtering")
	}

	return rb
}

// Order sets the order param or deletes it.
func (rb *RouteBuilder) Order(s string) *RouteBuilder {
	if s != "" {
		rb.v.Set("order", s)
	} else {
		rb.v.Del("order")
	}

	return rb
}

// Filter sets the filter param or deletes it.
func (rb *RouteBuilder) Filter(s string) *RouteBuilder {
	if s != "" {
		rb.v.Set("filter", s)
	} else {
		rb.v.Del("filter")
	}

	return rb
}

// Q sets the q param or deletes it.
func (rb *RouteBuilder) Q(s string) *RouteBuilder {
	if s != "" {
		rb.v.Set("q", s)
	} else {
		rb.v.Del("q")
	}

	return rb
}

// String implements fmt.Stringer and returns the finished url.
func (rb *RouteBuilder) String(isSandbox ...bool) string {
	if rb.err != nil {
		return "err: " + rb.err.Error()
	}
	host := BaseAPIURL
	if len(isSandbox) > 0 && isSandbox[0] {
		host = BaseSandboxAPIURL
	}

	return (&url.URL{
		Scheme:   "https",
		Host:     host,
		Path:     "/open_api/" + rb.version + rb.path,
		RawQuery: (rb.v).Encode(),
	}).String()
}
