package main

import (
	"encoding/json"
	"fmt"
	"log"

	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
)

func main() {
	fmt.Println("Testing GMV Max Campaign Info method")

	// Mock service for testing structure
	// In real usage, you would initialize with actual credentials
	// service, err := v13.New(logger, accessToken, appID, secret)

	// Test the parameter structure
	params := v13.GMVMaxCampaignInfoParams{
		AdvertiserID: "7015148626926141442",
		CampaignID:   "test-campaign-id",
	}

	jsonData, err := json.MarshalIndent(params, "", "  ")
	if err != nil {
		log.Fatal(err)
	}

	fmt.Println("GMV Max Campaign Info Parameters:")
	fmt.Println(string(jsonData))

	// Test response structure
	mockResponse := v13.GMVMaxCampaignInfoResponse{
		Code:      0,
		Message:   "OK",
		RequestID: "test-request-id",
		Data: v13.GMVMaxCampaignInfoData{
			AdvertiserID:         "7015148626926141442",
			CampaignID:           "test-campaign-id",
			CampaignName:         "Test GMV Max Campaign",
			OperationStatus:      "ENABLE",
			StoreID:              "test-store-id",
			ShoppingAdsType:      "PRODUCT",
			OptimizationGoal:     "VALUE",
			ROIProtectionEnabled: true,
			DeepBidType:          "VO_MIN_ROAS",
			RoasBid:              2.5,
			Budget:               100.0,
			ScheduleType:         "SCHEDULE_FROM_NOW",
			ScheduleStartTime:    "2024-01-01 00:00:00",
			Placements:           []string{"PLACEMENT_TIKTOK"},
			LocationIDs:          []string{"6252001"},
			AgeGroups:            []string{"AGE_25_34"},
			IdentityList:         []v13.IdentityInfo{},
			ItemList:             []v13.ItemInfo{},
		},
	}

	jsonData, err = json.MarshalIndent(mockResponse, "", "  ")
	if err != nil {
		log.Fatal(err)
	}

	fmt.Println("\nMock GMV Max Campaign Info Response:")
	fmt.Println(string(jsonData))

	fmt.Println("\n✅ GMV Max Campaign Info structures are working correctly!")
}
