package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	"github.com/dev-networldasia/dspgos/sctx"
	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
)

// Example demonstrating how to use GMV Max Campaigns List and Info methods
func main() {
	fmt.Println("GMV Max Campaigns Example")
	
	// Initialize the service (replace with your actual credentials)
	log := sctx.NewLogger()
	accessToken := "your-access-token"
	appID := "your-app-id"
	secret := "your-secret"
	
	service, err := v13.New(log, accessToken, appID, secret)
	if err != nil {
		log.Fatal("Failed to initialize service:", err)
	}
	
	advertiserID := "your-advertiser-id"
	
	// Step 1: List GMV Max Campaigns
	fmt.Println("\n=== Step 1: Listing GMV Max Campaigns ===")
	listParams := v13.GMVMaxCampaignsParams{
		AdvertiserID: advertiserID,
		Filtering: v13.GMVMaxCampaignsFiltering{
			GMVMaxPromotionTypes: []string{"PRODUCT_GMV_MAX", "LIVE_GMV_MAX"},
		},
		Page:     1,
		PageSize: 10,
	}
	
	listResp, err := service.GMVMaxCampaigns.List(context.Background(), &listParams)
	if err != nil {
		log.Fatal("Failed to list GMV Max campaigns:", err)
	}
	
	fmt.Printf("Found %d GMV Max campaigns\n", len(listResp.Data.List))
	
	// Step 2: Get detailed info for each campaign
	fmt.Println("\n=== Step 2: Getting Campaign Details ===")
	for i, campaign := range listResp.Data.List {
		fmt.Printf("\n--- Campaign %d: %s ---\n", i+1, campaign.CampaignName)
		
		infoParams := v13.GMVMaxCampaignInfoParams{
			AdvertiserID: advertiserID,
			CampaignID:   campaign.CampaignID,
		}
		
		infoResp, err := service.GMVMaxCampaigns.Info(context.Background(), &infoParams)
		if err != nil {
			fmt.Printf("Error getting info for campaign %s: %v\n", campaign.CampaignID, err)
			continue
		}
		
		// Display key information
		data := infoResp.Data
		fmt.Printf("Campaign ID: %s\n", data.CampaignID)
		fmt.Printf("Campaign Name: %s\n", data.CampaignName)
		fmt.Printf("Operation Status: %s\n", data.OperationStatus)
		fmt.Printf("Shopping Ads Type: %s\n", data.ShoppingAdsType)
		fmt.Printf("Budget: $%.2f\n", data.Budget)
		fmt.Printf("Optimization Goal: %s\n", data.OptimizationGoal)
		fmt.Printf("ROI Protection: %t\n", data.ROIProtectionEnabled)
		
		if data.DeepBidType == "VO_MIN_ROAS" && data.RoasBid > 0 {
			fmt.Printf("ROAS Target: %.2f\n", data.RoasBid)
		}
		
		fmt.Printf("Placements: %v\n", data.Placements)
		fmt.Printf("Schedule Type: %s\n", data.ScheduleType)
		fmt.Printf("Start Time: %s\n", data.ScheduleStartTime)
		
		if len(data.IdentityList) > 0 {
			fmt.Printf("Identities: %d\n", len(data.IdentityList))
		}
		
		if len(data.ItemList) > 0 {
			fmt.Printf("Items/Posts: %d\n", len(data.ItemList))
		}
		
		// Optionally print full JSON for detailed inspection
		if false { // Set to true to see full response
			jsonData, _ := json.MarshalIndent(infoResp, "", "  ")
			fmt.Printf("Full Response:\n%s\n", string(jsonData))
		}
	}
	
	fmt.Println("\n=== Example completed successfully! ===")
}
