package main

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/dev-networldasia/dspgos/sctx"
	v13 "github.com/dev-networldasia/tiktokservice/marketing/v13"
	"github.com/namsral/flag"
)

type tkConf struct {
	AppID       string
	Secret      string
	AccessToken string
}

var conf tkConf

func init() {
	flag.StringVar(&conf.AppID, "tiktok-app-id", "", "tiktok app id")
	flag.StringVar(&conf.Secret, "tiktok-app-secret", "", "tiktok app secrect")
	flag.StringVar(&conf.AccessToken, "tiktok-access-token", "", "tiktok access token")

	flag.Parse()
}
func main() {
	fmt.Println("------------------- test service tiktok --------------")
	log := sctx.NewLogger()
	service, err := v13.New(log, conf.AccessToken, conf.AppID, conf.Secret)
	if err != nil {
		fmt.Println("err - ", err)
	}
	fmt.Println("-----------------connect service tiktok thành công----------------")

	advMayBi := "7015148626926141442"
	var jsonData []byte

	// list, err := service.AdAccounts.ListAdAccount(context.Background())
	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }
	// fmt.Printf("--->> list = %+v \n ", list)

	// advInfo, err := service.AdAccounts.GetAdAccount(context.Background(), []string{advMayBi})
	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }
	// fmt.Printf("--->> advInfo = %+v \n ", advInfo)

	// camp
	// param := v13.CamppaignParams{
	// 	AdvertiserID: advMayBi,
	// }
	// fmt.Println("param - ", param)

	// campResp, err := service.Campaigns.List(context.Background(), &param)
	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }
	// fmt.Printf("--->> campResp = %+v \n ", *campResp)

	//ad
	// param := v13.AdgroupParams{
	// 	AdvertiserID: advMayBi,
	// 	PageSize:     100,
	// 	// Fields:       []string{"creative_material_mode"},
	// 	Filtering: &v13.AdgroupFiltering{
	// 		// CampaignIDs: &[]string{"****************"},
	// 		AdgroupIDs: &[]string{"****************"},
	// 		// 	// AdIDs: []string{"****************"},
	// 	},
	// 	// AdGroupIds: []string{"****************"},
	// }

	// fmt.Println("param - ", param)

	// adResp, err := service.Adgroups.List(context.Background(), &param)
	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }

	// jsonData, _ := json.MarshalIndent(adResp, "", "  ")
	// fmt.Println("\n-------- adResp -------> \n", string(jsonData))

	//Video
	// param2 := v13.IdentityVideoParams{
	// 	AdvertiserID:           advMayBi,
	// 	IdentityID:             "15718214-e67f-4efc-979c-751be1f67236",
	// 	IdentityType:           "BC_AUTH_TT",
	// 	IdentityAuthorizedBcID: "7015073949394436098",
	// 	ItemType:               "VIDEO",
	// }

	// post, err := service.Identities.ListVideo(context.Background(), &param2)
	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }

	// jsonData, _ := json.MarshalIndent(post, "", "  ")
	// fmt.Println("\n-------- post -------> \n", string(jsonData))

	//Audience
	// param := v13.AudienceParam{
	// 	AdvertiserID: advMayBi,
	// }

	// audienceResp, err := service.Audiences.List(context.Background(), &param)

	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }

	// jsonData, _ := json.MarshalIndent(audienceResp, "", "  ")
	// fmt.Println("\n-------- audienceResp -------> \n", string(jsonData))

	//Creative
	// typ := v13.ShoppingAdsType("LIVE")
	// placement := v13.Placement("PLACEMENT_TIKTOK")
	// // promotionType := "WEBSITE"
	// // callToAction := "SHOP_NOW"
	// //
	// verVidStrate := v13.VerticalVideoStrategy("SINGLE_VIDEO")
	// //
	// tiktokItemID := "7459629231597784327"
	// // AdText := "Set lụa nhẹ nhàng xinh yêu cho nàng thơ MAYBI ✨🌷 #maybi #maybifashion #fyp #trending #viral #xuhuong #thinhhanh #thoitrangnu #outfit #ootd #fashionreview #aokieu #aolua #chanvaylua #setxinh"
	// identityAuthorizedBcID := "7015073949394436098"
	// param := v13.AdPreview{
	// 	AdvertiserID:           advMayBi,
	// 	PreviewType:            v13.PREVIEW_TYPE_ADS_CREATION,
	// 	ObjectiveType:          v13.OBJECTIVE_TYPE_PRODUCT_SALES,
	// 	ShoppingAdsType:        typ,
	// 	Placement:              placement,
	// 	IdentityID:             "15718214-e67f-4efc-979c-751be1f67236",
	// 	IdentityType:           "BC_AUTH_TT",
	// 	IdentityAuthorizedBcID: &identityAuthorizedBcID,
	// 	AdFormat:               "LIVE_CONTENT",
	// 	VerticalVideoStrategy:  &verVidStrate,
	// 	TikTokItemID:           &tiktokItemID,
	// 	// AdText:                 AdText,
	// }
	// jsonData, _ := json.MarshalIndent(param, "", "  ")
	// fmt.Println("\n-------- param -------> \n", string(jsonData))
	// creativeResp, err := service.Creative.CreateAdPreview(context.Background(), param)

	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }

	// jsonData, _ = json.MarshalIndent(creativeResp, "", "  ")
	// fmt.Println("\n-------- creativeResp -------> \n", string(jsonData))

	// callToAction := "SHOP_NOW"
	// videoID := "v10033g50000cngokunog65rpnlolr9g"
	// landingPageUrl := "https://maybi.com/?utm_source=tiktok&utm_medium=paid&utm_id=__CAMPAIGN_ID__&utm_campaign=__CAMPAIGN_NAME__"
	// ad := v13.AdPreview{
	// 	AdvertiserID:   advMayBi,
	// 	PreviewType:    v13.PREVIEW_TYPE_ADS_CREATION,
	// 	IdentityID:     "7341301053357113346",
	// 	IdentityType:   "CUSTOMIZED_USER",
	// 	ObjectiveType:  v13.OBJECTIVE_TYPE_REACH,
	// 	AdFormat:       v13.AD_FORMAT_SINGLE_VIDEO,
	// 	AdText:         "Test",
	// 	CallToAction:   &callToAction,
	// 	VideoID:        &videoID,
	// 	LandingPageUrl: &landingPageUrl,
	// 	Placement:      "PLACEMENT_TIKTOK",
	// }
	// jsonData, _ := json.MarshalIndent(ad, "", "  ")
	// fmt.Println("\n-------- ad -------> \n", string(jsonData))
	// creativeResp, err := service.Creative.CreateAdPreview(context.Background(), ad)

	// if err != nil {
	// 	fmt.Println("err - ", err)
	// }

	// jsonData, _ = json.MarshalIndent(creativeResp, "", "  ")
	// fmt.Println("\n-------- creativeResp -------> \n", string(jsonData))

	// Example: Get AdGroups with ShoppingAdsType = "LIVE" (GMV Max)

	// Example: Get GMV Max Campaigns (PRODUCT_GMV_MAX)
	gmvParams := v13.GMVMaxCampaignsParams{
		AdvertiserID: advMayBi,
		Filtering: v13.GMVMaxCampaignsFiltering{
			// GMVMaxPromotionTypes: []string{"PRODUCT_GMV_MAX"},
			GMVMaxPromotionTypes: []string{"LIVE_GMV_MAX", "PRODUCT_GMV_MAX"},
		},
		Page:     1,
		PageSize: 10,
	}
	gmvResp, err := service.GMVMaxCampaigns.List(context.Background(), &gmvParams)
	if err != nil {
		fmt.Println("err - ", err)
	}
	jsonData, _ = json.MarshalIndent(gmvResp, "", "  ")
	fmt.Println("\n-------- GMV Max Campaigns -------> \n", string(jsonData))

	// Get details of the first GMV Max Campaign if available
	if gmvResp != nil && len(gmvResp.Data.List) > 0 {
		firstCampaign := gmvResp.Data.List[0]
		fmt.Printf("\n-------- Getting details for Campaign ID: %s --------\n", firstCampaign.CampaignID)

		infoParams := v13.GMVMaxCampaignInfoParams{
			AdvertiserID: advMayBi,
			CampaignID:   firstCampaign.CampaignID,
		}

		infoResp, err := service.GMVMaxCampaigns.Info(context.Background(), &infoParams)
		if err != nil {
			fmt.Println("err getting campaign info - ", err)
		} else {
			jsonData, _ = json.MarshalIndent(infoResp, "", "  ")
			fmt.Println("\n-------- GMV Max Campaign Info -------> \n", string(jsonData))
		}
	}
}
