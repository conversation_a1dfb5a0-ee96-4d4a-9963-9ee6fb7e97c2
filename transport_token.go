package tiktokservice

import (
	"context"
	"net/http"
)

type tokenTransport struct {
	token string
	next  http.RoundTripper
}

type token<PERSON>ey struct{}
type ctxSkipAccessTokenKey struct{}

var tk tokenKey

func newTokenTransport(token string, next http.RoundTripper) http.RoundTripper {
	if next == nil {
		next = http.DefaultTransport
	}

	return &tokenTransport{
		token: token,
		next:  next,
	}
}

func (t *tokenTransport) RoundTrip(r *http.Request) (*http.Response, error) {
	ctx := r.Context()

	if r.Header.Get("Content-Type") == "" {
		r.Header.Set("Content-Type", "application/json")
	}
	if !skipAccessToken(ctx) {
		token := t.getAccessToken(ctx)
		if token != "" {
			r.Header.Set("Access-Token", token)
		}
	}

	return t.next.RoundTrip(r)
}

// SetPageAccessToken adds token to the context to be used for making requests.
func SetPageAccessToken(ctx context.Context, token string) context.Context {
	if token == "" {
		return ctx
	}

	return context.WithValue(ctx, tk, token)
}

func (t *tokenTransport) getAccessToken(ctx context.Context) string {
	token, ok := ctx.Value(tk).(string)
	if ok && token != "" {
		return token
	}

	return t.token
}

func WithSkipAccessToken(ctx context.Context) context.Context {
	return context.WithValue(ctx, ctxSkipAccessTokenKey{}, true)
}

func skipAccessToken(ctx context.Context) bool {
	val, ok := ctx.Value(ctxSkipAccessTokenKey{}).(bool)
	return ok && val
}
