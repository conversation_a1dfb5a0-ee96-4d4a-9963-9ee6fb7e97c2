package tiktokservice

import (
	"encoding/json"
	"net/http"

	"github.com/dev-networldasia/dspgos/sctx"
)

type xAppUsage struct {
	CallCount    int `json:"call_count"`
	TotalCputime int `json:"total_cputime"`
	TotalTime    int `json:"total_time"`
}

type logAppUsageTransport struct {
	l    sctx.Logger
	next http.RoundTripper
}

func newLogAppUsageTransport(l sctx.Logger, next http.RoundTripper) http.RoundTripper {
	if l == nil {
		l = sctx.NewLogger()
	}

	if next == nil {
		next = http.DefaultTransport
	}

	return &logAppUsageTransport{
		l:    l,
		next: next,
	}
}

func (t *logAppUsageTransport) RoundTrip(r *http.Request) (*http.Response, error) {
	resp, err := t.next.RoundTrip(r)

	if resp != nil {
		xau := resp.Header.Get("x-app-usage")
		if xau == "" {
			return resp, err
		}

		xAppUsage := &xAppUsage{}
		err := json.Unmarshal([]byte(xau), xAppUsage)
		if err != nil {
			t.l.With("x-app-usage", xau).Errorf("encountered an error when unmarshalling into *xAppUsage: %v", err)
		}

		if xAppUsage.CallCount > 0 || xAppUsage.TotalCputime > 0 || xAppUsage.TotalTime > 0 {
			t.l.Withs(sctx.Fields{
				"call_count":    xAppUsage.CallCount,
				"total_cputime": xAppUsage.TotalCputime,
				"total_time":    xAppUsage.TotalTime,
			}).Info("got x-app-usage")
		}
	}

	return resp, err
}
