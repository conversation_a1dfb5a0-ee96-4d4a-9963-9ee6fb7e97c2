package v13

import (
	"context"

	"github.com/dev-networldasia/dspgos/sctx"
	"github.com/dev-networldasia/tiktokservice"
)

const Version = "v1.3"

type Service struct {
	*tiktokservice.Client
	Accounts        *AccountService
	AdAccounts      *AdAccountService
	Adgroups        *AdgroupService
	Campaigns       *CampaignService
	Ads             *AdService
	Identities      *IdentityService
	Pixels          *PixelService
	Targeting       *TargetingService
	Audiences       *AudienceService
	Posts           *PostService
	Creative        *CreativeService
	GMVMaxCampaigns *GMVMaxCampaignsService
}

func New(l sctx.Logger, accessToken, appID, secret string) (*Service, error) {
	c := tiktokservice.NewClient(l, accessToken, appID, secret)
	service := &Service{
		Client:          c,
		AdAccounts:      &AdAccountService{c},
		Accounts:        &AccountService{c},
		Campaigns:       &CampaignService{c},
		Adgroups:        &AdgroupService{c},
		Ads:             &AdService{c},
		Identities:      &IdentityService{c},
		Pixels:          &PixelService{c},
		Targeting:       &TargetingService{c},
		Audiences:       &AudienceService{c},
		Posts:           &PostService{c},
		Creative:        &CreativeService{c},
		GMVMaxCampaigns: &GMVMaxCampaignsService{c},
	}
	_, err := service.Accounts.TokenInfo(context.Background())
	if err != nil {
		return nil, err
	}

	return service, nil
}
