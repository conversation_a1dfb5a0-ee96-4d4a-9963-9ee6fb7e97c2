package v13

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_IDENTITY_GET = "/identity/get/"
	// ENDPOINT_IDENTITY_LIVE_GET = "/identity/live/get/"
)

var ()

/**
 * Identity
 */
type Identity struct {
	IdentityID             string           `json:"identity_id"`
	IdentityType           string           `json:"identity_type"`
	IdentityAuthorizedBcID string           `json:"identity_authorized_bc_id"`
	DisplayName            string           `json:"display_name"`
	ProfileImage           string           `json:"profile_image"`
	AvailableStatus        *AvailableStatus `json:"available_status"`

	CanPushVideo     bool `json:"can_push_video"`
	CanPullVideo     bool `json:"can_pull_video"`
	CanUseLiveAds    bool `json:"can_use_live_ads"`
	CanManageMessage bool `json:"can_manage_message"`
}

type IdentityType string

const (
	IdentityTypeCustomizedUser IdentityType = "CUSTOMIZED_USER"
	IdentityTypeAuthCode       IdentityType = "AUTH_CODE"
	IdentityTypeTtUser         IdentityType = "TT_USER"
	IdentityTypeBcAuthTt       IdentityType = "BC_AUTH_TT"
)

type AvailableStatus string

const (
	AvailableStatusAvailable          AvailableStatus = "AVAILABLE"
	AvailableStatusNoValidBindAccount AvailableStatus = "NO_VALID_BIND_ACCOUNT"
	AvailableStatusScopeUnavailable   AvailableStatus = "SCOPE_UNAVAILABLE"
	AvailableStatusIsPrivateAccount   AvailableStatus = "IS_PRIVATE_ACCOUNT"
	AvailableStatusNotBusinessAccount AvailableStatus = "NOT_BUSINESS_ACCOUNT"
)

type IdentityFiltering struct {
	Keyword string `json:"keyword,omitempty"`
}

type IdentityParams struct {
	AdvertiserID           string             `json:"advertiser_id"`
	IdentityType           IdentityType       `json:"identity_type"`
	IdentityAuthorizedBcID string             `json:"identity_authorized_bc_id,omitempty"`
	Filtering              *IdentityFiltering `json:"filtering,omitempty"`
	Page                   int                `json:"page,omitempty"`
	PageSize               int                `json:"page_size,omitempty"`
}

func (p *IdentityParams) validateBuildIdentityParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_IDENTITY_GET).SetParam("advertiser_id", p.AdvertiserID)

	if p.IdentityType != "" {
		rb.SetParam("identity_type", string(p.IdentityType))
	}

	if p.IdentityAuthorizedBcID != "" {
		rb.SetParam("identity_authorized_bc_id", p.IdentityAuthorizedBcID)
	}

	if p.Filtering != nil {
		filterJSON, err := json.Marshal(p.Filtering)
		if err != nil {
			return nil, ErrValidateFiltering
		}
		rb.SetParam("filtering", string(filterJSON))
	}

	if p.Page > 0 {
		rb.SetParam("page", strconv.Itoa(p.Page))
	} else {
		rb.SetParam("page", "1")
	}

	if p.PageSize > 0 {
		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
	} else {
		rb.SetParam("page_size", "100")
	}

	url := rb.String()
	return &url, nil
}

type IdentityService struct {
	c *tiktokservice.Client
}

type IdentityResp struct {
	Code      int           `json:"code"`
	Message   string        `json:"message"`
	RequestID string        `json:"request_id"`
	Data      *IdentityData `json:"data"`
}

type IdentityData struct {
	IdentityList []Identity             `json:"identity_list"`
	PageInfo     tiktokservice.PageInfo `json:"page_info"`
}

func (cs *IdentityService) List(ctx context.Context, params *IdentityParams) (*IdentityResp, error) {
	urlStr, err := params.validateBuildIdentityParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var identityResp IdentityResp
	if err := cs.c.GetJSON(ctx, *urlStr, &identityResp); err != nil {
		return nil, err
	}

	return &identityResp, nil
}
