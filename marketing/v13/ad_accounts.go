package v13

import (
	"context"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_ADV_LIST = "/oauth2/advertiser/get/"
	ENDPOINT_ADV_GET  = "/advertiser/info/"
)

type Advertiser struct {
	AdvertiserID            string  `json:"advertiser_id"`
	OwnerBCID               string  `json:"owner_bc_id,omitempty"`
	Status                  string  `json:"status"`
	Role                    string  `json:"role"`
	RejectionReason         string  `json:"rejection_reason,omitempty"`
	Name                    string  `json:"name"`
	Timezone                string  `json:"timezone"`
	DisplayTimezone         string  `json:"display_timezone"`
	Company                 string  `json:"company"`
	CompanyNameEditable     bool    `json:"company_name_editable"`
	Industry                string  `json:"industry"`
	Address                 string  `json:"address"`
	Country                 string  `json:"country"`
	AdvertiserAccountType   string  `json:"advertiser_account_type"`
	Currency                string  `json:"currency"`
	Contacter               string  `json:"contacter"`
	Email                   string  `json:"email"`
	CellphoneNumber         string  `json:"cellphone_number"`
	TelephoneNumber         string  `json:"telephone_number"`
	Language                string  `json:"language"`
	LicenseNo               string  `json:"license_no"`
	LicenseURL              string  `json:"license_url"`
	LicenseProvince         string  `json:"license_province"`
	LicenseCity             string  `json:"license_city"`
	PromotionArea           string  `json:"promotion_area"`
	PromotionCenterProvince string  `json:"promotion_center_province"`
	PromotionCenterCity     string  `json:"promotion_center_city"`
	Brand                   string  `json:"brand"`
	Description             string  `json:"description"`
	Balance                 float64 `json:"balance"`
	CreateTime              int64   `json:"create_time"`
}

type AdvertiserInfoResponse struct {
	Code      int            `json:"code"`
	Message   string         `json:"message"`
	RequestID string         `json:"request_id"`
	Data      AdvertiserData `json:"data"`
}

type AdvertiserData struct {
	List []Advertiser `json:"list"`
}

type AdvertiserResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Data      struct {
		List []AdvertiserAuthorizedData `json:"list"`
	} `json:"data"`
}

type AdvertiserAuthorizedData struct {
	AdvertiserID string `json:"advertiser_id"`
	Name         string `json:"advertiser_name"`
}

type AdAccountService struct {
	c *tiktokservice.Client
}

/**
 * Get authorized ad accounts
 * Use this endpoint to obtain a list of advertiser accounts that authorized an app.
 * Header : Access-Token - Required
 * Method : GET
 *
 * Param : app_id string - required
 * Parma : secret string - required
 */
func (aas *AdAccountService) ListAdAccount(ctx context.Context) (*AdvertiserResponse, error) {
	var res AdvertiserResponse
	rb := tiktokservice.NewRoute(Version, ENDPOINT_ADV_LIST).SetParams(map[string]string{
		"app_id": aas.c.AppId,
		"secret": aas.c.Secret,
	})
	if err := aas.c.GetJSON(ctx, rb.String(), &res); err != nil {
		return nil, err
	}
	return &res, nil
}

/**
 * Get ad account details
 * Use this endpoint to obtain the details of an advertiser's ad account.
 * Header : Access-Token - Required
 * Method : GET
 *
 * Param : advertiser_ids []string - required
 * Param : fields []string
 * Note : fields : company_name_editable return advertiser_id,company_name_editable
 */
func (aas *AdAccountService) GetAdAccount(ctx context.Context, advertiserIds []string, fields ...string) (*AdvertiserInfoResponse, error) {
	var res AdvertiserInfoResponse
	rb := tiktokservice.NewRoute(Version, ENDPOINT_ADV_GET).SetJSONArray("advertiser_ids", advertiserIds)

	if err := aas.c.GetJSON(ctx, rb.String(), &res); err != nil {
		return nil, err
	}
	return &res, nil
}
