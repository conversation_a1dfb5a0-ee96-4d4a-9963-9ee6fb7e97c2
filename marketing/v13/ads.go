package v13

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_AD_GET = "/ad/get/"
)

var ()

type AdService struct {
	c *tiktokservice.Client
}

type Ad struct {
	AdvertiseID string `json:"advertise_id"`
	CampaignID  string `json:"campaign_id"`
	AdGroupID   string `json:"adgroup_id"`
	AdID        string `json:"ad_id"`
	AdName      string `json:"ad_name"`

	CreateTime string `json:"create_time"`
	ModifyTime string `json:"modify_time"`

	IdentityID             *string `json:"identity_id"`               // Pointer for optionality
	IdentityType           string  `json:"identity_type"`             // ENUM: CUSTOMIZED_USER, AUTH_CODE, TT_USER, BC_AUTH_TT
	IdentityAuthorizedBcID *string `json:"identity_authorized_bc_id"` // Pointer for optionality

	CatalogID           *string   `json:"catalog_id"`            // Pointer for optionality
	ProductSpecificType string    `json:"product_specific_type"` // ENUM: ALL, PRODUCT_SET, CUSTOMIZED_PRODUCTS
	ItemGroupIDs        *[]string `json:"item_group_ids"`        // This was string, changed to *string based on AdsResp
	ProductSetID        *string   `json:"product_set_id"`        // Pointer for optionality
	SkuIDs              string    `json:"sku_ids"`               // Original type
	VehicleIDs          string    `json:"vehicle_ids"`           // Original type

	ShowcaseProducts []ShowcaseProducts `json:"showcase_products"`

	AdFormat                  string    `json:"ad_format"`               // ENUM: SINGLE_IMAGE, SINGLE_VIDEO, LIVE_CONTENT, CAROUSEL_ADS, CATALOG_CAROUSEL
	VerticalVideoStrategy     string    `json:"vertical_video_strategy"` // ENUM: UNSET, SINGLE_VIDEO, CATALOG_VIDEOS, CATALOG_UPLOADED_VIDEOS, LIVE_STREAM
	DynamicFormat             string    `json:"dynamic_format"`          // ENUM: UNSET, DYNAMIC_CREATIVE
	VideoID                   *string   `json:"video_id"`
	ImageIDs                  *[]string `json:"image_ids"`
	CarouselImageIndex        int64     `json:"carousel_image_index"`
	EndCardCta                string    `json:"end_card_cta"`               // ENUM: SEARCH_INVENTORY, LEARN_MORE, SHOP_NOW, SIGN_UP, CONTACT_US, BOOK_NOW, READ_MORE, VIEW_MORE, ORDER_NOW
	AutoDisclaimerTypes       []string  `json:"auto_disclaimer_types"`      // ENUM: EMISSION, DISCOUNT
	ProductDisplayFieldList   []string  `json:"product_display_field_list"` // ENUM: DEALER_NAME, MAKE, MODEL, YEAR, MILEAGE, PRICE, SALE_PRICE, EXTERIOR_COLOR, TRIM, ADDRESS_CITY, VEHICLE_STATE
	MusicID                   *string   `json:"music_id"`
	TikTokItemID              *string   `json:"tiktok_item_id"`
	PromotionalMusicDisabled  bool      `json:"promotional_music_disabled"`
	ItemDuetStatus            string    `json:"item_duet_status"`   // ENUM: ENABLE, DISABLE
	ItemStitchStatus          string    `json:"item_stitch_status"` // ENUM: ENABLE, DISABLE
	DarkPostStatus            string    `json:"dark_post_status"`   // ENUM: ON, OFF
	BrandedContentDisabled    bool      `json:"branded_content_disabled"`
	ShoppingAdsVideoPackageID *string   `json:"shopping_ads_video_package_id"`
	AdText                    *string   `json:"ad_text"`
	AdTexts                   *string   `json:"ad_texts"`

	CallToAction            string     `json:"call_to_action"` // ENUM: (See TikTok's Call-to-action enum for full list, e.g., SHOP_NOW, LEARN_MORE, DOWNLOAD)
	CallToActionID          *string    `json:"call_to_action_id"`
	CardID                  *string    `json:"card_id"`
	LandingPageUrl          *string    `json:"landing_page_url"`
	UtmParams               []UtmParam `json:"utm_params"`
	PageID                  *int64     `json:"page_id"`
	CppUrl                  *string    `json:"cpp_url"`
	TikTokPageCategory      string     `json:"tiktok_page_category"` // ENUM: PROFILE_PAGE, OTHER_TIKTOK_PAGE, TIKTOK_INSTANT_PAGE
	Deeplink                *string    `json:"deeplink"`
	DeeplinkType            string     `json:"deeplink_type"`        // ENUM: NORMAL, DEFERRED_DEEPLINK
	DeeplinkFormatType      string     `json:"deeplink_format_type"` // ENUM: UNIVERSAL_OR_APP_LINK, SCHEME_LINK, NONE
	DeeplinkUtmParams       []UtmParam `json:"deeplink_utm_params"`
	ShoppingAdsFallbackType string     `json:"shopping_ads_fallback_type"` // ENUM: DEFAULT, CUSTOM, SHOPPING_ADS
	FallbackType            string     `json:"fallback_type"`              // ENUM: APP_INSTALL, WEBSITE, UNSET
	DynamicDestination      string     `json:"dynamic_destination"`        // ENUM: DLP, UNSET
	Destination             string     `json:"destination"`

	AutoMessageID              *string                    `json:"auto_message_id"`
	AigcDisclosureType         string                     `json:"aigc_disclosure_type"` // ENUM: SELF_DISCLOSURE, NOT_DECLARED
	DisclaimerType             string                     `json:"disclaimer_type"`      // ENUM: TEXT_LINK, TEXT_ONLY
	DisclaimerText             *DisclaimerText            `json:"disclaimer_text"`
	DisclaimerClickableTexts   []DisclaimerClickableTexts `json:"disclaimer_clickable_texts"`
	TrackingPixelID            *int64                     `json:"tracking_pixel_id"`
	TrackingAppID              *string                    `json:"tracking_app_id"`
	TrackingOfflineEventSetIDs []string                   `json:"tracking_offline_event_set_ids"`
	TrackingMessageEventSetID  *string                    `json:"tracking_message_event_set_id"`

	VastMoatEnabled           bool   `json:"vast_moat_enabled"`
	ViewabilityPostbidPartner string `json:"viewability_postbid_partner"` // ENUM: UNSET, MOAT, DOUBLE_VERIFY, IAS
	ViewablitityVastUrl       string `json:"viewability_vast_url"`
	BrandSafetyPostBidPartner string `json:"brand_safety_postbid_partner"` // ENUM: UNSET, DOUBLE_VERIFY, IAS, ZEFR
	BrandSafetyVastUrl        string `json:"brand_safety_vast_url"`

	ImpressionTrackingUrl *string `json:"impression_tracking_url"`
	ClickTrackingUrl      *string `json:"click_tracking_url"`
	PlayableUrl           *string `json:"playable_url"`

	OperationStatus string `json:"operation_status"` // ENUM: ENABLE, DISABLE, FROZEN
	SecondaryStatus string `json:"secondary_status"`

	AppName          *string `json:"app_name"`
	AvatarIconWebUri *string `json:"avatar_icon_web_uri"`

	CreativeType *string `json:"creative_type"`
}

type ShowcaseProducts struct {
	ItemGroupID string `json:"item_group_id"`
	StoreID     string `json:"store_id"`
	CatalogID   string `json:"catalog_id"`
}

type UtmParam struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type DisclaimerText struct {
	Text string `json:"text"`
}

type DisclaimerClickableTexts struct {
	Text string `json:"text"`
	Url  string `json:"url"`
}

type AdsResp struct {
	AdvertiserID string `json:"advertiser_id"`
	CampaignID   string `json:"campaign_id"`
	AdGroupID    string `json:"adgroup_id"`
	AdID         string `json:"ad_id"`
	AdName       string `json:"ad_name"`

	CreateTime string `json:"create_time"`
	ModifyTime string `json:"modify_time"`

	IdentityID             *string `json:"identity_id"`
	IdentityType           string  `json:"identity_type"`
	IdentityAuthorizedBcID *string `json:"identity_authorized_bc_id"`

	CatalogID *string `json:"catalog_id"`

	ProductSpecificType string    `json:"product_specific_type"`
	ItemGroupIDs        *[]string `json:"item_group_ids"`
	ProductSetID        *string   `json:"product_set_id"`

	ShowcaseProducts []ShowcaseProducts `json:"showcase_products"`

	AdFormat                  string     `json:"ad_format"`
	VerticalVideoStrategy     string     `json:"vertical_video_strategy"`
	DynamicFormat             string     `json:"dynamic_format"`
	VideoID                   *string    `json:"video_id"`
	ImageIDs                  *[]string  `json:"image_ids"`
	EndCardCta                string     `json:"end_card_cta"`
	MusicID                   *string    `json:"music_id"`
	TikTokItemID              *string    `json:"tiktok_item_id"`
	PromotionalMusicDisabled  bool       `json:"promotional_music_disabled"`
	ItemDuetStatus            string     `json:"item_duet_status"`
	ItemStitchStatus          string     `json:"item_stitch_status"`
	ShoppingAdsVideoPackageID *string    `json:"shopping_ads_video_package_id"`
	AdText                    *string    `json:"ad_text"`
	AdTexts                   *[]string  `json:"ad_texts"`
	AppName                   string     `json:"app_name"`
	AvatarIconWebUri          string     `json:"avatar_icon_web_uri"`
	CallToAction              string     `json:"call_to_action"`
	CallToActionID            *string    `json:"call_to_action_id"`
	CardID                    *string    `json:"card_id"`
	LandingPageUrl            *string    `json:"landing_page_url"`
	LandingPageUrls           []string   `json:"landing_page_urls"`
	UtmParams                 []UtmParam `json:"utm_params"`
	PageID                    *int64     `json:"page_id"`
	TikTokPageCategory        string     `json:"tiktok_page_category"`
	Deeplink                  *string    `json:"deeplink"`
	DeeplinkType              string     `json:"deeplink_type"`
	DeeplinkFormatType        string     `json:"deeplink_format_type"`
	DeeplinkUtmParams         []UtmParam `json:"deeplink_utm_params"`
	AutoMessageID             *string    `json:"auto_message_id"`
	CreativeType              *string    `json:"creative_type"`
	DynamicDestination        string     `json:"dynamic_destination"`
	Destination               string     `json:"destination"`

	TrackingPixelID            *int64   `json:"tracking_pixel_id"`
	TrackingAppID              *string  `json:"tracking_app_id"`
	TrackingOfflineEventSetIDs []string `json:"tracking_offline_event_set_ids"`
	TrackingMessageEventSetID  string   `json:"tracking_message_event_set_id"`

	OperationStatus string `json:"operation_status"`
	SecondaryStatus string `json:"secondary_status"`
}

type AdFormat string

const (
	AD_FORMAT_SINGLE_VIDEO     AdFormat = "SINGLE_VIDEO"
	AD_FORMAT_SINGLE_IMAGE     AdFormat = "SINGLE_IMAGE"
	AD_FORMAT_CAROUSEL_ADS     AdFormat = "CAROUSEL_ADS"
	AD_FORMAT_CATALOG_CAROUSEL AdFormat = "CATALOG_CAROUSEL"
	AD_FORMAT_LIVE_CONTENT     AdFormat = "LIVE_CONTENT"
)

type AdParams struct {
	AdvertiserID                string       `json:"advertiser_id"`
	Fields                      []string     `json:"fields"`
	ExcludeFieldTypesInResponse []string     `json:"exclude_field_types_in_response"`
	Filtering                   *AdFiltering `json:"filtering"`
	Page                        int          `json:"page,omitempty"`
	PageSize                    int          `json:"page_size,omitempty"`
}

type AdFiltering struct {
	CampaignIDs []string `json:"campaign_ids,omitempty"`
	AdGroupIDs  []string `json:"adgroup_ids,omitempty"`
	AdIDs       []string `json:"ad_ids,omitempty"`
}

func (p *AdParams) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_AD_GET).SetParam("advertiser_id", p.AdvertiserID)

	if len(p.Fields) > 0 {
		rb.SetJSONArray("fields", p.Fields)
	}

	if len(p.ExcludeFieldTypesInResponse) > 0 {
		rb.SetJSONArray("exclude_field_types_in_response", p.ExcludeFieldTypesInResponse)
	}

	if p.Filtering != nil {
		filterJSON, err := json.Marshal(p.Filtering)
		if err != nil {
			return nil, ErrValidateFiltering
		}
		rb.SetParam("filtering", string(filterJSON))
	}

	if p.Page > 0 {
		rb.SetParam("page", strconv.Itoa(p.Page))
	} else {
		rb.SetParam("page", "1")
	}

	if p.PageSize > 0 {
		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
	} else {
		rb.SetParam("page_size", "100")
	}

	url := rb.String()
	return &url, nil
}

type AdListResponse struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	RequestID string `json:"request_id"`
	Data      AdData `json:"data"`
}

type AdData struct {
	List     []AdsResp              `json:"list"`
	PageInfo tiktokservice.PageInfo `json:"page_info"`
}

func (cs *AdService) List(ctx context.Context, params *AdParams) (*AdListResponse, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var adResp AdListResponse
	if err := cs.c.GetJSON(ctx, *urlStr, &adResp); err != nil {
		return nil, err
	}

	return &adResp, nil
}
