package v13

import (
	"context"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"github.com/dev-networldasia/tiktokservice"
)

var (
	ErrCustomAudienceIDsRequired           = errors.New("custom_audience_ids is required")
	ErrInvalidArrayLengthCustomAudienceIDs = errors.New("Invalid array length for custom_audience_ids")
)

const (
	ENDPOINT_AUDIENCE_LIST       = "/dmp/custom_audience/list/"
	ENDPOINT_AUDIENCE_GET_DETAIL = "/dmp/custom_audience/get/"
)

type Audience struct {
	AudienceID      string        `json:"audience_id"`
	Msg             string        `json:"msg"`
	AudienceSubType string        `json:"audience_sub_type"`
	ErrorMsg        string        `json:"error_msg"`
	IsValid         bool          `json:"is_valid"`
	IsExpiring      bool          `json:"is_expiring"`
	ExpiredTime     string        `json:"expired_time"`
	Name            string        `json:"name"`
	Rule            string        `json:"rule"`
	IsAutoRefresh   bool          `json:"is_auto_refresh"`
	Shared          bool          `json:"shared"`
	IsCreator       bool          `json:"is_creator"`
	OwnerID         string        `json:"owner_id"`
	CreateTime      string        `json:"create_time"`
	Type            AudienceType  `json:"type"`
	CoverNum        int64         `json:"cover_num"`
	CalculateType   string        `json:"calculate_type"`
	LookalikeSpec   LookalikeSpec `json:"lookalike_spec,omitempty"`
}

type AudienceSubType string

const (
	AudienceSubTypeNormal         AudienceSubType = "NORMAL"
	AudienceSubTypeReachFrequency AudienceSubType = "REACH_FREQUENCY"
)

type AudienceSize string

const (
	AudienceSizeNarrow   AudienceSize = "NARROW"
	AudienceSizeBalanced AudienceSize = "BALANCED"
	AudienceSizeBroad    AudienceSize = "BROAD"
)

type AudienceItem struct {
	AudienceID    string        `json:"audience_id"`
	Name          string        `json:"name"`
	AudienceType  AudienceType  `json:"audience_type"`
	Shared        bool          `json:"shared"`
	IsCreator     bool          `json:"is_creator"`
	CoverNum      int64         `json:"cover_num"`
	CreateTime    string        `json:"create_time"`
	ExpiredTime   string        `json:"expired_time"`
	IsValid       bool          `json:"is_valid"`
	IsExpiring    bool          `json:"is_expiring"`
	CalculateType CalculateType `json:"calculate_type,omitempty"`
}

type MobileOS string

const (
	MobileOSAll     MobileOS = "ALL"
	MobileOSAndroid MobileOS = "ANDROID"
	MobileOSIOS     MobileOS = "IOS"
)

type Placement string

const (
	PlacementTikTok          Placement = "PLACEMENT_TIKTOK"
	PlacementGlobalAppBundle Placement = "PLACEMENT_GLOBAL_APP_BUNDLE"
	PlacementTopBuzz         Placement = "PLACEMENT_TOPBUZZ"
	PlacementPangle          Placement = "PLACEMENT_PANGLE"
	PlacementAll             Placement = "ALL"
)

type LookalikeSpec struct {
	SourceAudienceID string       `json:"source_audience_id,omitempty"`
	IncludeSource    bool         `json:"include_source,omitempty"`
	MobileOS         MobileOS     `json:"mobile_os,omitempty"`
	Placements       []Placement  `json:"placements,omitempty"`
	LocationIDs      []string     `json:"location_ids,omitempty"`
	AudienceSize     AudienceSize `json:"audience_size,omitempty"`
}

type AudienceType string

const (
	AudienceTypeCustomerFile    AudienceType = "Customer File Audience"
	AudienceTypeEngagement      AudienceType = "Engagement Audience"
	AudienceTypeAppActivity     AudienceType = "App Activity Audience"
	AudienceTypeWebsite         AudienceType = "Website Traffic Audience"
	AudienceTypeLookalike       AudienceType = "Lookalike Audience"
	AudienceTypeLeadGeneration  AudienceType = "Lead Generation Audience"
	AudienceTypeBusinessAccount AudienceType = "Business Account Audience"
	AudienceTypeShopActivity    AudienceType = "Shop Activity Audience"
	AudienceTypePartner         AudienceType = "Partner Audience"
	AudienceTypeChallenge       AudienceType = "Challenge Audience"
	AudienceTypePremium         AudienceType = "Premium Audience"
)

type CalculateType string

const (
	CalculateTypeFirstSHA256   CalculateType = "FIRST_SHA256"
	CalculateTypeFirstMD5      CalculateType = "FIRST_MD5"
	CalculateTypeEmailSHA256   CalculateType = "EMAIL_SHA256"
	CalculateTypePhoneSHA256   CalculateType = "PHONE_SHA256"
	CalculateTypeIDFASHA256    CalculateType = "IDFA_SHA256"
	CalculateTypeIDFAMD5       CalculateType = "IDFA_MD5"
	CalculateTypeGAIDSHA256    CalculateType = "GAID_SHA256"
	CalculateTypeGAIDMD5       CalculateType = "GAID_MD5"
	CalculateTypeMultipleTypes CalculateType = "MULTIPLE_TYPES"
)

type AudienceParam struct {
	AdvertiserID      string   `json:"advertiser_id"`
	CustomAudienceIDs []string `json:"custom_audience_ids,omitempty"`
	Page              int      `json:"page,omitempty"`
	PageSize          int      `json:"page_size,omitempty"`
}

type AudienceService struct {
	c *tiktokservice.Client
}

func (p *AudienceParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}

	rb := tiktokservice.NewRoute(Version, ENDPOINT_AUDIENCE_LIST).SetParam("advertiser_id", p.AdvertiserID)

	if len(p.CustomAudienceIDs) > 0 {
		rb.SetParam("custom_audience_ids", strings.Join(p.CustomAudienceIDs, ","))
	}

	if p.Page > 0 {
		rb.SetParam("page", strconv.Itoa(p.Page))
	} else {
		rb.SetParam("page", "1")
	}

	if p.PageSize > 0 {
		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
	} else {
		rb.SetParam("page_size", "100")
	}

	url := rb.String()

	return &url, nil
}

type AudienceListResponse struct {
	Code      int              `json:"code"`
	Message   string           `json:"message"`
	RequestID string           `json:"request_id"`
	Data      AudienceListData `json:"data"`
}

type AudienceListData struct {
	List     []AudienceItem `json:"list"`
	PageInfo tiktokservice.PageInfo
}

func (s *AudienceService) List(ctx context.Context, param *AudienceParam) (*AudienceListResponse, error) {
	urlStr, err := param.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var audienceResp AudienceListResponse

	if err := s.c.GetJSON(ctx, *urlStr, &audienceResp); err != nil {
		return nil, err
	}

	return &audienceResp, nil
}

type AudienceGetDetailParam struct {
	AdvertiserID      string   `json:"advertiser_id"`
	CustomAudienceIDs []string `json:"custom_audience_ids"`
	HistorySize       int      `json:"history_size"`
}

type AudienceGetDetailResp struct {
	Code      int                   `json:"code"`
	Message   string                `json:"message"`
	RequestID string                `json:"request_id"`
	Data      AudienceGetDetailData `json:"data"`
}

type AudienceGetDetailData struct {
	List []AudienceGetDetailList `json:"list"`
}

type AudienceGetDetailList struct {
	AudienceDetails Audience          `json:"audience_details"`
	AudienceHistory []AudienceHistory `json:"audience_history"`
}

type AudienceHistory struct {
	Action       string `json:"action"`
	ActionDetail string `json:"action_detail"`
	Editor       string `json:"editor"`
	Msg          string `json:"msg"`
	OptTime      string `json:"opt_time"`
}

func (p *AudienceGetDetailParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}

	if len(p.CustomAudienceIDs) < 0 {
		return nil, ErrCustomAudienceIDsRequired
	}

	rb := tiktokservice.NewRoute(Version, ENDPOINT_AUDIENCE_GET_DETAIL).SetParam("advertiser_id", p.AdvertiserID)

	if len(p.CustomAudienceIDs) > 0 {
		rb.SetJSONArray("custom_audience_ids", p.CustomAudienceIDs)
	}

	if p.HistorySize > 0 {
		rb.SetParam("history_size", strconv.Itoa(p.HistorySize))
	}

	url := rb.String()

	return &url, nil
}

func (s *AudienceService) GetDetail(ctx context.Context, param *AudienceGetDetailParam) (*AudienceGetDetailResp, error) {
	urlStr, err := param.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var audienceResp AudienceGetDetailResp

	if err := s.c.GetJSON(ctx, *urlStr, &audienceResp); err != nil {
		return nil, err
	}

	return &audienceResp, nil
}
