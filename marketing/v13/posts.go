package v13

import (
	"context"
	"fmt"
	"strconv"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_IDENTITY_VIDEO_GET = "/identity/video/get/"
)

type PostService struct {
	c *tiktokservice.Client
}

type PostParams struct {
	AdvertiserID           string       `json:"advertiser_id"`
	IdentityID             string       `json:"identity_id"`
	IdentityType           IdentityType `json:"identity_type"`
	IdentityAuthorizedBcID string       `json:"identity_authorized_bc_id,omitempty"`
	ItemType               ItemType     `json:"item_type,omitempty"`
	Keyword                string       `json:"keyword,omitempty"`
	Cursor                 string       `json:"cursor,omitempty"`
	Count                  int          `json:"count,omitempty"`
}

type PostsResp struct {
	Code      int        `json:"code"`
	Message   string     `json:"message"`
	RequestID string     `json:"request_id"`
	Data      *PostsData `json:"data"`
}

type PostsData struct {
	VideoList []Post `json:"video_list"`
	Cursor    string `json:"cursor"`
	Has<PERSON>ore   bool   `json:"has_more"`
	RequestID string `json:"request_id"`
}

type Post struct {
	ItemType       ItemType      `json:"item_type"`
	ItemID         string        `json:"item_id"`
	Status         ItemStatus    `json:"status"`
	Text           string        `json:"text"`
	AuthInfo       *AuthInfo     `json:"auth_info,omitempty"`
	AnchorList     []Anchor      `json:"anchor_list,omitempty"`
	ProductRegions []string      `json:"product_regions,omitempty"`
	VideoInfo      *VideoInfo    `json:"video_info,omitempty"`
	CarouselInfo   *CarouselInfo `json:"carousel_info,omitempty"`
	MusicInfo      *MusicInfo    `json:"music_info,omitempty"`
}

type AuthInfo struct {
	AdAuthStatus    string `json:"ad_auth_status"`
	AuthStartTime   string `json:"auth_start_time"`
	AuthEndTime     string `json:"auth_end_time"`
	InviteStartTime string `json:"invite_start_time"`
}

type Anchor struct {
	AnchorID string       `json:"anchor_id"`
	Title    string       `json:"title"`
	Status   AnchorStatus `json:"status"`
	URL      string       `json:"url"`
}

type VideoInfo struct {
	BitRate   int     `json:"bit_rate"`
	Duration  float64 `json:"duration"`
	Size      int     `json:"size"`
	Height    int     `json:"height"`
	Width     int     `json:"width"`
	PosterURL string  `json:"poster_url"`
	Signature string  `json:"signature"`
	URL       string  `json:"url"`
	Format    string  `json:"format"`
}

type CarouselInfo struct {
	ImageInfo []ImageInfo `json:"image_info"`
	MusicInfo *Music      `json:"music_info"`
}

type Music struct {
	MusicURL      string `json:"music_url"`
	MusicDuration int    `json:"music_duration"`
}

type ImageInfo struct {
	ImageID     string `json:"image_id"`
	ImageURL    string `json:"image_url"`
	ImageHeight int    `json:"image_height"`
	ImageWidth  int    `json:"image_width"`
}

type MusicInfo struct {
	MusicID       string `json:"music_id"`
	MusicURL      string `json:"music_url"`
	MusicDuration int    `json:"music_duration"`
}

type ItemType string

const (
	ItemTypeVideo    ItemType = "VIDEO"
	ItemTypeCarousel ItemType = "CAROUSEL"
)

type ItemStatus string

const (
	ItemStatusHesitateRecommend ItemStatus = "ITEM_STATUS_HESITATE_RECOMMEND"
	ItemStatusOnlyFriendSee     ItemStatus = "STATUS_ONLY_FRIEND_SEE"
	ItemStatusOnlyAuthorSee     ItemStatus = "ITEM_STATUS_ONLY_AUTHOR_SEE"
)

type AnchorStatus string

const (
	AnchorCheckIng     AnchorStatus = "CHECK_ING"
	AnchorCheckFailed  AnchorStatus = "CHECK_FAILED"
	AnchorCheckSuccess AnchorStatus = "CHECK_SUCCESS"
)

func (p *PostParams) validateBuildPostParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_IDENTITY_VIDEO_GET).SetParam("advertiser_id", p.AdvertiserID)

	if p.IdentityID != "" {
		rb.SetParam("identity_id", p.IdentityID)
	}

	if p.IdentityType != "" {
		rb.SetParam("identity_type", string(p.IdentityType))
	}

	if p.IdentityAuthorizedBcID != "" {
		rb.SetParam("identity_authorized_bc_id", p.IdentityAuthorizedBcID)
	}

	if p.IdentityType != "" {
		rb.SetParam("identity_type", string(p.IdentityType))
	}

	if p.Keyword != "" {
		rb.SetParam("keyword", p.Keyword)
	}

	if p.Cursor != "" {
		rb.SetParam("cursor", p.Cursor)
	}

	if p.Count > 0 {
		rb.SetParam("count", strconv.Itoa(p.Count))
	} else {
		rb.SetParam("count", "20")
	}

	url := rb.String()
	return &url, nil
}

func (cs *PostService) List(ctx context.Context, params *PostParams) (*PostsResp, error) {
	urlStr, err := params.validateBuildPostParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var identityVideoResp PostsResp
	if err := cs.c.GetJSON(ctx, *urlStr, &identityVideoResp); err != nil {
		return nil, err
	}

	return &identityVideoResp, nil
}
