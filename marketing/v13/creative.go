package v13

import (
	"context"
	"errors"

	"github.com/dev-networldasia/tiktokservice"
)

var (
	ErrValidatePreviewType   = errors.New("preview_type is required")
	ErrValidateObjectiveType = errors.New("objective_type is required")
	ErrValidateIdentityId    = errors.New("identity_id is required")
	ErrValidateIdentityType  = errors.New("identity_type is required")
	ErrValidateIdentityBcId  = errors.New("identity_authorized_bc_id is required")
	ErrValidateAdFormat      = errors.New("ad_format is required")
)

const (
	END_POINT_ADS_PREVIEW_CREATE = "/creative/ads_preview/create/"
)

type AdPreview struct {
	AdvertiserID  string        `json:"advertiser_id" bson:"advertiser_id"`
	PreviewType   PreviewType   `json:"preview_type" bson:"preview_type"`
	ObjectiveType ObjectiveType `json:"objective_type" bson:"objective_type"`
	IdentityID    string        `json:"identity_id" bson:"identity_id"`
	IdentityType  IdentityType  `json:"identity_type" bson:"identity_type"`
	AdFormat      AdFormat      `json:"ad_format" bson:"ad_format"`
	AdText        string        `json:"ad_text,omitempty" bson:"ad_text"`

	IsSmartPerformanceCampaign bool            `json:"is_smart_performance_campaign,omitempty" bson:"is_smart_performance_campaign,omitempty"`
	Placement                  Placement       `json:"placement,omitempty" bson:"placement,omitempty"`
	PreviewFormat              PreviewFormat   `json:"preview_format,omitempty" bson:"preview_format,omitempty"`
	ShoppingAdsType            ShoppingAdsType `json:"shopping_ads_type,omitempty" bson:"shopping_ads_type,omitempty"`
	ProductSource              ProductSource   `json:"product_source,omitempty" bson:"product_source,omitempty"`
	StoreID                    string          `json:"store_id,omitempty" bson:"store_id,omitempty"`
	StoreAuthorizedBcID        string          `json:"store_authorized_bc_id,omitempty" bson:"store_authorized_bc_id,omitempty"`
	PromotionType              string          `json:"promotion_type,omitempty" bson:"promotion_type,omitempty"`
	IdentityAuthorizedBcID     *string         `json:"identity_authorized_bc_id,omitempty" bson:"identity_authorized_bc_id,omitempty"`

	VideoID            *string   `json:"video_id,omitempty" bson:"video_id,omitempty"`
	ImageIDs           *[]string `json:"image_ids,omitempty" bson:"image_ids,omitempty"`
	EndCardCta         *[]string `json:"end_card_cta,omitempty" bson:"end_card_cta,omitempty"`
	MusicID            *string   `json:"music_id,omitempty" bson:"music_id,omitempty"`
	TikTokItemID       *string   `json:"tiktok_item_id,omitempty" bson:"tiktok_item_id,omitempty"`
	CarouselImageIndex *int      `json:"carousel_image_index,omitempty" bson:"carousel_image_index,omitempty"`
	CallToAction       *string   `json:"call_to_action,omitempty" bson:"call_to_action,omitempty"`
	CallToActionID     *string   `json:"call_to_action_id,omitempty" bson:"call_to_action_id,omitempty"`
	CardID             *string   `json:"card_id,omitempty" bson:"card_id,omitempty"`
	LandingPageUrl     *string   `json:"landing_page_url,omitempty" bson:"landing_page_url,omitempty"`
	PageID             *string   `json:"page_id,omitempty" bson:"page_id,omitempty"`
	CatalogID          *string   `json:"catalog_id,omitempty" bson:"catalog_id,omitempty"`

	ShowcaseProducts    []ShowcaseProduct     `json:"showcase_products,omitempty" bson:"showcase_products,omitempty"`
	ItemGroupIDs        []string              `json:"item_group_ids,omitempty" bson:"item_group_ids,omitempty"`
	AutoDisclaimerTypes []AutoDisclaimerTypes `json:"auto_disclaimer_types,omitempty" bson:"auto_disclaimer_types,omitempty"`

	ProductSpecificType       *ProductSpecificType     `json:"product_specific_type,omitempty" bson:"product_specific_type,omitempty"`
	ProductSetID              *string                  `json:"product_set_id,omitempty" bson:"product_set_id,omitempty"`
	SkuIDs                    *string                  `json:"sku_ids,omitempty" bson:"sku_ids,omitempty"`
	VehicleIDs                *string                  `json:"vehicle_ids,omitempty" bson:"vehicle_ids,omitempty"`
	CatalogAuthorizedBcID     *string                  `json:"catalog_authorized_bc_id,omitempty" bson:"catalog_authorized_bc_id,omitempty"`
	DynamicFormat             *DynamicFormat           `json:"dynamic_format,omitempty" bson:"dynamic_format,omitempty"`
	VerticalVideoStrategy     *VerticalVideoStrategy   `json:"vertical_video_strategy,omitempty" bson:"vertical_video_strategy,omitempty"`
	ShoppingAdsVideoPackageID *string                  `json:"shopping_ads_video_package_id,omitempty" bson:"shopping_ads_video_package_id,omitempty"`
	ShoppingAdsFallbackType   *ShoppingAdsFallbackType `json:"shopping_ads_fallback_type,omitempty" bson:"shopping_ads_fallback_type,omitempty"`
	DynamicDestination        *string                  `json:"dynamic_destination,omitempty" bson:"dynamic_destination,omitempty"`
	InstantProductPageUsed    *bool                    `json:"instant_product_page_used,omitempty" bson:"instant_product_page_used,omitempty"`
}

type ShowcaseProduct struct {
	ItemGroupID string `json:"item_group_id" bson:"item_group_id"`
	StoreID     string `json:"store_id" bson:"store_id"`
}

type PreviewType string

const (
	PREVIEW_TYPE_ADS_CREATION = "ADS_CREATION"
)

type PreviewFormat string

const (
	PREVIEW_FORMAT_IN_FEED             PreviewFormat = "IN_FEED"
	PREVIEW_FORMAT_SEARCH_RESULTS      PreviewFormat = "SEARCH_RESULTS"
	PREVIEW_FORMAT_SEARCH_FEED         PreviewFormat = "SEARCH_FEED"
	PREVIEW_FORMAT_TIKTOK_LITE         PreviewFormat = "TIKTOK_LITE"
	PREVIEW_FORMAT_PRODUCT_SEARCH      PreviewFormat = "PRODUCT_SEARCH"
	PREVIEW_FORMAT_PRODUCT_SHOP_CENTER PreviewFormat = "PRODUCT_SHOP_CENTER"

	// Pangle Formats
	PREVIEW_FORMAT_INTERSTITIAL           PreviewFormat = "INTERSTITIAL"
	PREVIEW_FORMAT_REWARDED               PreviewFormat = "REWARDED"
	PREVIEW_FORMAT_APP_OPEN               PreviewFormat = "APP_OPEN"
	PREVIEW_FORMAT_NORMAL_BANNER          PreviewFormat = "NORMAL_BANNER"
	PREVIEW_FORMAT_VIDEO_THUMBNAIL_BANNER PreviewFormat = "VIDEO_THUMBNAIL_BANNER"
	PREVIEW_FORMAT_SMALL_VIDEO_BANNER     PreviewFormat = "SMALL_VIDEO_BANNER"
	PREVIEW_FORMAT_ICON_ONLY_BANNER       PreviewFormat = "ICON_ONLY_BANNER"
	PREVIEW_FORMAT_NORMAL_NATIVE          PreviewFormat = "NORMAL_NATIVE"
	PREVIEW_FORMAT_VIDEO_THUMBNAIL_NATIVE PreviewFormat = "VIDEO_THUMBNAIL_NATIVE"
)

const (
	ProductSourceStore    ProductSource = "STORE"
	ProductSourceShowcase ProductSource = "SHOWCASE"
)

type ProductSpecificType string

const (
	PRODUCT_SPECIFIC_TYPE_ALL                 ProductSpecificType = "ALL"
	PRODUCT_SPECIFIC_TYPE_PRODUCT_SET         ProductSpecificType = "PRODUCT_SET"
	PRODUCT_SPECIFIC_TYPE_CUSTOMIZED_PRODUCTS ProductSpecificType = "CUSTOMIZED_PRODUCTS"
)

type AutoDisclaimerTypes string

const (
	AUTO_DISCLAIMER_TYPES_EMISSION AutoDisclaimerTypes = "EMISSION"
	AUTO_DISCLAIMER_TYPES_DISCOUNT AutoDisclaimerTypes = "DISCOUNT"
)

type DynamicFormat string

const (
	DYNAMIC_FORMAT_DYNAMIC_CREATIVE DynamicFormat = "DYNAMIC_CREATIVE"
	DYNAMIC_FORMAT_UNSET            DynamicFormat = "UNSET"
)

type VerticalVideoStrategy string

const (
	VERTICAL_VIDEO_STRATEGY_UNSET          VerticalVideoStrategy = "UNSET"
	VERTICAL_VIDEO_STRATEGY_SINGLE_VIDEO   VerticalVideoStrategy = "SINGLE_VIDEO"
	VERTICAL_VIDEO_STRATEGY_CATALOG_VIDEOS VerticalVideoStrategy = "CATALOG_VIDEOS"
	VERTICAL_VIDEO_STRATEGY_LIVE_STREAM    VerticalVideoStrategy = "LIVE_STREAM"
)

type ShoppingAdsFallbackType string

const (
	SHOPPING_ADS_FALLBACK_TYPE_DEFAULT      ShoppingAdsFallbackType = "DEFAULT"
	SHOPPING_ADS_FALLBACK_TYPE_CUSTOM       ShoppingAdsFallbackType = "CUSTOM"
	SHOPPING_ADS_FALLBACK_TYPE_SHOPPING_ADS ShoppingAdsFallbackType = "SHOPPING_ADS"
)

type DynamicDestination string

const (
	DYNAMIC_DESTINATION_UNSET DynamicDestination = "UNSET"
	DYNAMIC_DESTINATION_DLP   DynamicDestination = "DLP"
)

type AdsPreviewResp struct {
	Code      int            `json:"code" bson:"code"`
	Message   string         `json:"message" bson:"message"`
	RequestID string         `json:"request_id" bson:"request_id"`
	Data      AdsPreviewData `json:"data" bson:"data"`
}

type AdsPreviewData struct {
	PreviewLink string `json:"preview_link" bson:"preview_link"`
	Iframe      string `json:"iframe" bson:"iframe"`
	Tips        []Tip  `json:"tips" bson:"tips"`
}

type Tip struct {
	Placement string   `json:"placement" bson:"placement"`
	Messages  []string `json:"messages" bson:"messages"`
}

type CreativeService struct {
	c *tiktokservice.Client
}

func (cv *CreativeService) CreateAdPreview(ctx context.Context, req AdPreview) (*AdsPreviewResp, error) {
	var adPreviewResp AdsPreviewResp
	rb := tiktokservice.NewRoute(Version, END_POINT_ADS_PREVIEW_CREATE)

	if err := cv.c.PostJSON(ctx, rb.String(), req, &adPreviewResp); err != nil {
		return nil, err
	}

	return &adPreviewResp, nil
}
