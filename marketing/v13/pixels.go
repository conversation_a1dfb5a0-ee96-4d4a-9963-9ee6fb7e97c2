package v13

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_PIXEL_LIST = "/pixel/list/"
)

type PixelParam struct {
	AdvertiserID string          `json:"advertiser_id"`
	Code         string          `json:"code,omitempty"`
	PixelID      string          `json:"pixel_id,omitempty"`
	Name         string          `json:"name,omitempty"`
	OrderBy      OrderBy         `json:"order_by,omitempty"`
	Filtering    *PixelFiltering `json:"filtering,omitempty"`
	Page         int             `json:"page,omitempty"`
	PageSize     int             `json:"page_size,omitempty"`
}

type PixelFiltering struct {
	AvailableForCatalogOnly bool `json:"available_for_catalog_only"`
}

type OrderBy string

const (
	ORDER_BY_EARLIEST_CREATE = "EARLIEST_CREATE"
	ORDER_BY_LATEST_CREATE   = "LATEST_CREATE"
)

type PixelService struct {
	c *tiktokservice.Client
}

type PixelResp struct {
	Code      int    `json:"code"`
	Message   string `json:"message"`
	Data      any    `json:"data"`
	RequestID string `json:"request_id"`
}

func (p *PixelParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_PIXEL_LIST).SetParam("advertiser_id", p.AdvertiserID)

	if p.Code != "" {
		rb.SetParam("code", p.Code)
	}

	if p.PixelID != "" {
		rb.SetParam("pixel_id", p.PixelID)
	}

	if p.Name != "" {
		rb.SetParam("name", p.Name)
	}

	if p.OrderBy != "" {
		rb.SetParam("order_by", string(p.OrderBy))
	}

	if p.Filtering != nil {
		filterJSON, err := json.Marshal(p.Filtering)
		if err != nil {
			return nil, ErrValidateFiltering
		}
		rb.SetParam("filtering", string(filterJSON))
	}

	if p.PageSize > 0 {
		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
	} else {
		rb.SetParam("page_size", "20")
	}

	if p.Page > 0 {
		rb.SetParam("page", strconv.Itoa(p.Page))
	} else {
		rb.SetParam("page", "1")
	}

	url := rb.String()
	return &url, nil
}

func (ps *PixelService) List(ctx context.Context, params *PixelParam) (*PixelResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var pixelResp PixelResp
	if err := ps.c.GetJSON(ctx, *urlStr, &pixelResp); err != nil {
		return nil, err
	}

	return &pixelResp, nil
}
