package v13

import (
	"context"

	"github.com/dev-networldasia/tiktokservice"
)

// https://business-api.tiktok.com/portal/docs?id=****************#Advertiser%20Status
const (
	ENDPOINT_TOKEN_INFO = "/tt_user/token_info/get/"
)

type AccountService struct {
	c *tiktokservice.Client
}

type TokenInfoResponse struct {
	Code      int            `json:"code"`
	Message   string         `json:"message"`
	RequestID string         `json:"request_id"`
	Data      *TokenInfoData `json:"data"`
}

type TokenInfoData struct {
	AppID     string `json:"app_id"`
	Scope     string `json:"scope"`
	CreatorId string `json:"creator_id"`
}

type TokenInfoRequest struct {
	AppID       string `json:"app_id"`
	AccessToken string `json:"access_token"`
}

/**
 * Get the authorized TikTok account permission scopes via access token
 * Header : Content-Type - Required
 *
 * Param : app_id string - required
 * Parma : access_token string - required
 */
func (as *AccountService) TokenInfo(ctx context.Context) (*TokenInfoResponse, error) {
	var res TokenInfoResponse
	req := TokenInfoRequest{
		AppID:       as.c.AppId,
		AccessToken: as.c.AccessToken,
	}

	rb := tiktokservice.NewRoute(Version, ENDPOINT_TOKEN_INFO)
	ctxs := tiktokservice.WithSkipAccessToken(ctx)
	if err := as.c.PostJSON(ctxs, rb.String(), req, &res); err != nil {
		return nil, err
	}

	return &res, nil
}
