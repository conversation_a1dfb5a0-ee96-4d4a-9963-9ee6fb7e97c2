package v13

//https://business-api.tiktok.com/portal/docs?id=****************#Advertiser%20Status
const (
	//Advertiser Billing Type
	TRANS_TYPE_TRANSFER = "TRANS_TYPE_TRANSFER"
	TRANS_TYPE_TAX      = "TRANS_TYPE_TAX"
	TRANS_TYPE_COST     = "TRANS_TYPE_COST"

	// Advertiser Display Status (advertiser_status)
	SHOW_ACCOUNT_STATUS_NOT_APPROVED = "SHOW_ACCOUNT_STATUS_NOT_APPROVED"
	SHOW_ACCOUNT_STATUS_APPROVED     = "SHOW_ACCOUNT_STATUS_APPROVED"
	SHOW_ACCOUNT_STATUS_IN_REVIEW    = "SHOW_ACCOUNT_STATUS_IN_REVIEW"
	SHOW_ACCOUNT_STATUS_PUNISHED     = "SHOW_ACCOUNT_STATUS_PUNISHED"

	//adv roles
	ROLE_ADVERTISER       = "ROLE_ADVERTISER"
	ROLE_CHILD_ADVERTISER = "ROLE_CHILD_ADVERTISER"
	ROLE_CHILD_AGENT      = "ROLE_CHILD_AGENT"
	ROLE_AGENT            = "ROLE_AGENT"

	//adv status
	STATUS_DISABLE                = "STATUS_DISABLE"
	STATUS_PENDING_CONFIRM        = "STATUS_PENDING_CONFIRM"
	STATUS_PENDING_VERIFIED       = "STATUS_PENDING_VERIFIED"
	STATUS_CONFIRM_FAIL           = "STATUS_CONFIRM_FAIL"
	STATUS_ENABLE                 = "STATUS_ENABLE"
	STATUS_CONFIRM_FAIL_END       = "STATUS_CONFIRM_FAIL_END"
	STATUS_PENDING_CONFIRM_MODIFY = "STATUS_PENDING_CONFIRM_MODIFY"
	STATUS_CONFIRM_MODIFY_FAIL    = "STATUS_CONFIRM_MODIFY_FAIL"
	STATUS_LIMIT                  = "STATUS_LIMIT"
	STATUS_WAIT_FOR_BPM_AUDIT     = "STATUS_WAIT_FOR_BPM_AUDIT"
	STATUS_WAIT_FOR_PUBLIC_AUTH   = "STATUS_WAIT_FOR_PUBLIC_AUTH"
	STATUS_SELF_SERVICE_UNAUDITED = "STATUS_SELF_SERVICE_UNAUDITED"
	STATUS_CONTRACT_PENDING       = "STATUS_CONTRACT_PENDING"

	//Business Center Status
	REVIEWING = "REVIEWING"
	DENY      = "DENY"
	ENABLE    = "ENABLE"
	PUNISH    = "PUNISH"

	//Business Center Type
	NORMAL              = "NORMAL"
	DIRECT              = "DIRECT"
	AGENCY              = "AGENCY"
	SELF_SERVICE        = "SELF_SERVICE"
	SELF_SERVICE_AGENCY = "SELF_SERVICE_AGENCY"

	//Ad Delivery Speed (pacing)
	PACING_MODE_SMOOTH = "PACING_MODE_SMOOTH"
	PACING_MODE_FAST   = "PACING_MODE_FAST"
)
