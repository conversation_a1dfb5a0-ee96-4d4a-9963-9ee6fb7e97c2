package v13

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_ADGROUP_GET = "/adgroup/get/"
)

type AdgroupService struct {
	c *tiktokservice.Client
}

type AdgroupRes struct {
	Actions                      []Action              `json:"actions" bson:"actions"`
	AdgroupAppProfilePageState   *string               `json:"adgroup_app_profile_page_state" bson:"adgroup_app_profile_page_state"`
	AdgroupID                    string                `json:"adgroup_id" bson:"adgroup_id"`
	AdgroupName                  string                `json:"adgroup_name" bson:"adgroup_name"`
	AdvertiserID                 string                `json:"advertiser_id" bson:"advertiser_id"`
	AgeGroups                    []AgeGroup            `json:"age_groups" bson:"age_groups"`
	AppDownloadURL               *string               `json:"app_download_url" bson:"app_download_url"`
	AppID                        *string               `json:"app_id" bson:"app_id"`
	AppType                      *AppType              `json:"app_type" bson:"app_type"`
	AttributionEventCount        AttributionEventCount `json:"attribution_event_count" bson:"attribution_event_count"`
	AudienceIDs                  []string              `json:"audience_ids" bson:"audience_ids"`
	AutoTargetingEnabled         bool                  `json:"auto_targeting_enabled" bson:"auto_targeting_enabled"`
	AutomatedKeywordsEnabled     *bool                 `json:"automated_keywords_enabled" bson:"automated_keywords_enabled"`
	BidDisplayMode               BidDisplayMode        `json:"bid_display_mode" bson:"bid_display_mode"`
	BidPrice                     float64               `json:"bid_price" bson:"bid_price"`
	BidType                      BidType               `json:"bid_type" bson:"bid_type"`
	BillingEvent                 BillingEvent          `json:"billing_event" bson:"billing_event"`
	BrandSafetyPartner           *string               `json:"brand_safety_partner" bson:"brand_safety_partner"`
	BrandSafetyType              BrandSafetyType       `json:"brand_safety_type" bson:"brand_safety_type"`
	Budget                       float64               `json:"budget" bson:"budget"`
	BudgetMode                   BudgetMode            `json:"budget_mode" bson:"budget_mode"`
	CampaignID                   string                `json:"campaign_id" bson:"campaign_id"`
	CampaignName                 string                `json:"campaign_name" bson:"campaign_name"`
	CategoryExclusionIDs         []string              `json:"category_exclusion_ids" bson:"category_exclusion_ids"`
	CategoryID                   string                `json:"category_id" bson:"category_id"`
	ClickAttributionWindow       AttributionWindow     `json:"click_attribution_window" bson:"click_attribution_window"`
	CommentDisabled              bool                  `json:"comment_disabled" bson:"comment_disabled"`
	ContextualTagIDs             []string              `json:"contextual_tag_ids" bson:"contextual_tag_ids"`
	ConversionBidPrice           float64               `json:"conversion_bid_price" bson:"conversion_bid_price"`
	ConversionWindow             *string               `json:"conversion_window" bson:"conversion_window"`
	CreateTime                   string                `json:"create_time" bson:"create_time"`
	CreativeMaterialMode         CreativeMaterialMode  `json:"creative_material_mode" bson:"creative_material_mode"`
	Dayparting                   string                `json:"dayparting" bson:"dayparting"`
	DeepBidType                  DeepBidType           `json:"deep_bid_type" bson:"deep_bid_type"`
	DeepCpaBid                   float64               `json:"deep_cpa_bid" bson:"deep_cpa_bid"`
	DeepFunnelEventSource        *string               `json:"deep_funnel_event_source" bson:"deep_funnel_event_source"`
	DeepFunnelEventSourceID      *string               `json:"deep_funnel_event_source_id" bson:"deep_funnel_event_source_id"`
	DeepFunnelOptimizationEvent  *string               `json:"deep_funnel_optimization_event" bson:"deep_funnel_optimization_event"`
	DeepFunnelOptimizationStatus *string               `json:"deep_funnel_optimization_status" bson:"deep_funnel_optimization_status"`
	DeliveryMode                 *string               `json:"delivery_mode" bson:"delivery_mode"`
	DeviceModelIDs               []string              `json:"device_model_ids" bson:"device_model_ids"`
	DevicePriceRanges            []int                 `json:"device_price_ranges" bson:"device_price_ranges"`
	ExcludedAudienceIDs          []string              `json:"excluded_audience_ids" bson:"excluded_audience_ids"`
	ExcludedCustomActions        []string              `json:"excluded_custom_actions" bson:"excluded_custom_actions"`
	FeedType                     *string               `json:"feed_type" bson:"feed_type"`
	Frequency                    *int                  `json:"frequency" bson:"frequency"`
	FrequencySchedule            *int                  `json:"frequency_schedule" bson:"frequency_schedule"`
	Gender                       Gender                `json:"gender" bson:"gender"`
	HouseholdIncome              []string              `json:"household_income" bson:"household_income"`
	IdentityID                   string                `json:"identity_id" bson:"identity_id"`
	IdentityType                 *string               `json:"identity_type" bson:"identity_type"`
	IncludedCustomActions        []string              `json:"included_custom_actions" bson:"included_custom_actions"`
	InterestCategoryIDs          []string              `json:"interest_category_ids" bson:"interest_category_ids"`
	InterestKeywordIDs           []string              `json:"interest_keyword_ids" bson:"interest_keyword_ids"`
	InventoryFilterEnabled       bool                  `json:"inventory_filter_enabled" bson:"inventory_filter_enabled"`
	Ios14QuotaType               Ios14QuotaType        `json:"ios14_quota_type" bson:"ios14_quota_type"`
	IsHfss                       bool                  `json:"is_hfss" bson:"is_hfss"`
	IsNewStructure               bool                  `json:"is_new_structure" bson:"is_new_structure"`
	IsSmartPerformanceCampaign   bool                  `json:"is_smart_performance_campaign" bson:"is_smart_performance_campaign"`
	IspIDs                       []string              `json:"isp_ids" bson:"isp_ids"`
	Keywords                     *string               `json:"keywords" bson:"keywords"`
	Languages                    []string              `json:"languages" bson:"languages"`
	LocationIDs                  []string              `json:"location_ids" bson:"location_ids"`
	ModifyTime                   string                `json:"modify_time" bson:"modify_time"`
	NetworkTypes                 []string              `json:"network_types" bson:"network_types"`
	NextDayRetention             *string               `json:"next_day_retention" bson:"next_day_retention"`
	OperatingSystems             []string              `json:"operating_systems" bson:"operating_systems"`
	OperationStatus              OperationStatus       `json:"operation_status" bson:"operation_status"`
	OptimizationEvent            OptimizationEvent     `json:"optimization_event" bson:"optimization_event"`
	OptimizationGoal             OptimizationGoal      `json:"optimization_goal" bson:"optimization_goal"`
	Pacing                       PacingMode            `json:"pacing" bson:"pacing"`
	PixelID                      *string               `json:"pixel_id" bson:"pixel_id"`
	PlacementType                PlacementType         `json:"placement_type" bson:"placement_type"`
	Placements                   []string              `json:"placements" bson:"placements"`
	ProductSource                ProductSource         `json:"product_source" bson:"product_source"`
	PromotionType                string                `json:"promotion_type" bson:"promotion_type"`
	PurchasedImpression          *string               `json:"purchased_impression" bson:"purchased_impression"`
	PurchasedReach               *string               `json:"purchased_reach" bson:"purchased_reach"`
	RfEstimatedCpr               *string               `json:"rf_estimated_cpr" bson:"rf_estimated_cpr"`
	RfEstimatedFrequency         *string               `json:"rf_estimated_frequency" bson:"rf_estimated_frequency"`
	RfPurchasedType              *string               `json:"rf_purchased_type" bson:"rf_purchased_type"`
	ScheduleEndTime              string                `json:"schedule_end_time" bson:"schedule_end_time"`
	ScheduleInfos                *string               `json:"schedule_infos" bson:"schedule_infos"`
	ScheduleStartTime            string                `json:"schedule_start_time" bson:"schedule_start_time"`
	ScheduleType                 *string               `json:"schedule_type" bson:"schedule_type"`
	ScheduledBudget              float64               `json:"scheduled_budget" bson:"scheduled_budget"`
	SearchResultEnabled          bool                  `json:"search_result_enabled" bson:"search_result_enabled"`
	SecondaryOptimizationEvent   *string               `json:"secondary_optimization_event" bson:"secondary_optimization_event"`
	SecondaryStatus              string                `json:"secondary_status" bson:"secondary_status"`
	ShareDisabled                bool                  `json:"share_disabled" bson:"share_disabled"`
	ShoppingAdsType              ShoppingAdsType       `json:"shopping_ads_type" bson:"shopping_ads_type"`
	SkipLearningPhase            bool                  `json:"skip_learning_phase" bson:"skip_learning_phase"`
	SmartAudienceEnabled         *bool                 `json:"smart_audience_enabled" bson:"smart_audience_enabled"`
	SmartInterestBehaviorEnabled *bool                 `json:"smart_interest_behavior_enabled" bson:"smart_interest_behavior_enabled"`
	SpendingPower                SpendingPower         `json:"spending_power" bson:"spending_power"`
	StatisticType                *string               `json:"statistic_type" bson:"statistic_type"`
	StoreAuthorizedBcID          string                `json:"store_authorized_bc_id" bson:"store_authorized_bc_id"`
	StoreID                      string                `json:"store_id" bson:"store_id"`
	VboWindow                    *string               `json:"vbo_window" bson:"vbo_window"`
	VideoDownloadDisabled        bool                  `json:"video_download_disabled" bson:"video_download_disabled"`
	ViewAttributionWindow        AttributionWindow     `json:"view_attribution_window" bson:"view_attribution_window"`
	RoasBid                      float64               `json:"roas_bid,omitempty" bson:"roas_bid,omitempty"`
	TiktokSubplacements          []string              `json:"tiktok_subplacements" bson:"tiktok_subplacements"`
	CarrierIDs                   []string              `json:"carrier_ids" bson:"carrier_ids"`
}

type Action struct {
	ActionScene       string   `json:"action_scene"`
	ActionPeriod      int      `json:"action_period"`
	VideoUserActions  []string `json:"video_user_actions"`
	ActionCategoryIDs []string `json:"action_category_ids"`
}

type AgeGroup string

const (
	Age13_17  AgeGroup = "AGE_13_17"
	Age18_24  AgeGroup = "AGE_18_24"
	Age25_34  AgeGroup = "AGE_25_34"
	Age35_44  AgeGroup = "AGE_35_44"
	Age45_54  AgeGroup = "AGE_45_54"
	Age55Plus AgeGroup = "AGE_55+"
)

type AppType string

const (
	AppTypeIOS     AppType = "APP_IOS"
	AppTypeAndroid AppType = "APP_ANDROID"
	AppTypeOthers  AppType = "APP_OTHERS"
)

type AttributionEventCount string

const (
	AttributionEventCountEvery AttributionEventCount = "EVERY"
	AttributionEventCountOnce  AttributionEventCount = "ONCE"
)

type BidDisplayMode string

const (
	BidDisplayModeCPM  BidDisplayMode = "CPM"
	BidDisplayModeCPV  BidDisplayMode = "CPV"
	BidDisplayModeCPC  BidDisplayMode = "CPC"
	BidDisplayModeCPMV BidDisplayMode = "CPMV"
)

type BidType string

const (
	BidTypeNoBid BidType = "BID_TYPE_NO_BID"
	BidTypeBid   BidType = "BID_TYPE_BID"
)

type BillingEvent string

const (
	BillingEventCPC  BillingEvent = "CPC"
	BillingEventCPM  BillingEvent = "CPM"
	BillingEventOCPM BillingEvent = "OCPM"
)

type BrandSafetyType string

const (
	BrandSafetyTypeNoBrandSafety BrandSafetyType = "NO_BRAND_SAFETY"
	BrandSafetyTypeLow           BrandSafetyType = "LOW"
	BrandSafetyTypeMedium        BrandSafetyType = "MEDIUM"
	BrandSafetyTypeHigh          BrandSafetyType = "HIGH"
)

type BudgetMode string

const (
	BudgetModeDay   BudgetMode = "BUDGET_MODE_DAY"
	BudgetModeTotal BudgetMode = "BUDGET_MODE_TOTAL"
)

type AttributionWindow string

const (
	AttributionWindowOneDay       AttributionWindow = "ONE_DAY"
	AttributionWindowSevenDays    AttributionWindow = "SEVEN_DAYS"
	AttributionWindowFourteenDays AttributionWindow = "FOURTEEN_DAYS"
)

type CreativeMaterialMode string

const (
	CreativeMaterialModeCustom CreativeMaterialMode = "CUSTOM"
	CreativeMaterialModeAuto   CreativeMaterialMode = "AUTO"
)

type DeepBidType string

const (
	DeepBidTypeVoHighestValue DeepBidType = "VO_HIGHEST_VALUE"
	DeepBidTypeVoAverageValue DeepBidType = "VO_AVERAGE_VALUE"
)

type Gender string

const (
	GenderMale   Gender = "GENDER_MALE"
	GenderFemale Gender = "GENDER_FEMALE"
	GenderAll    Gender = "GENDER_ALL"
)

type Ios14QuotaType string

const (
	Ios14QuotaTypeOccupied   Ios14QuotaType = "OCCUPIED"
	Ios14QuotaTypeUnoccupied Ios14QuotaType = "UNOCCUPIED"
)

type OperationStatus string

const (
	OperationStatusEnable  OperationStatus = "ENABLE"
	OperationStatusDisable OperationStatus = "DISABLE"
)

type OptimizationEvent string

const (
	OptimizationEventClick    OptimizationEvent = "CLICK"
	OptimizationEventInstall  OptimizationEvent = "INSTALL"
	OptimizationEventPurchase OptimizationEvent = "PURCHASE"
	OptimizationEventShopping OptimizationEvent = "SHOPPING"
)

type OptimizationGoal string

const (
	OptimizationGoalConversion OptimizationGoal = "CONVERSION"
	OptimizationGoalClick      OptimizationGoal = "CLICK"
	OptimizationGoalImpression OptimizationGoal = "IMPRESSION"
	OptimizationGoalReach      OptimizationGoal = "REACH"
	OptimizationGoalValue      OptimizationGoal = "VALUE"
)

type PacingMode string

const (
	PacingModeSmooth   PacingMode = "PACING_MODE_SMOOTH"
	PacingModeNoPacing PacingMode = "PACING_MODE_NO_PACING"
)

type PlacementType string

const (
	PlacementTypeNormal    PlacementType = "PLACEMENT_TYPE_NORMAL"
	PlacementTypeAutomatic PlacementType = "PLACEMENT_TYPE_AUTOMATIC"
)

type ShoppingAdsType string

const (
	ShoppingAdsTypeLive ShoppingAdsType = "LIVE"
	ShoppingAdsTypeVSA  ShoppingAdsType = "VSA"
)

type ProductSource string

const (
	ProductSourceUnset   ProductSource = "UNSET"
	ProductSourceCatalog ProductSource = "CATALOG"
)

type SpendingPower string

const (
	SpendingPowerAll  SpendingPower = "ALL"
	SpendingPowerHigh SpendingPower = "HIGH"
)

type SecondaryStatus string

const (
	SecondaryStatusAdgroupStatusCampaignDisable SecondaryStatus = "ADGROUP_STATUS_CAMPAIGN_DISABLE"
	SecondaryStatusAdgroupStatusEnable          SecondaryStatus = "ADGROUP_STATUS_ENABLE"
	SecondaryStatusAdgroupStatusDisable         SecondaryStatus = "ADGROUP_STATUS_DISABLE"
)

type AdgroupParams struct {
	AdvertiserID                string            `json:"advertiser_id"`
	Fields                      []string          `json:"fields"`
	ExcludeFieldTypesInResponse []string          `json:"exclude_field_types_in_response"`
	Filtering                   *AdgroupFiltering `json:"filtering"`
	Page                        int               `json:"page,omitempty"`
	PageSize                    int               `json:"page_size,omitempty"`
}

type AdgroupFiltering struct {
	CampaignIDs *[]string `json:"campaign_ids,omitempty"`
	AdgroupIDs  *[]string `json:"adgroup_ids,omitempty"`
	AdgroupName *string   `json:"adgroup_name,omitempty"`
}

type AdgroupListResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	RequestID string      `json:"request_id"`
	Data      AdgroupData `json:"data"`
}

type AdgroupData struct {
	List     []AdgroupRes           `json:"list"`
	PageInfo tiktokservice.PageInfo `json:"page_info"`
}

func (p *AdgroupParams) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_ADGROUP_GET).SetParam("advertiser_id", p.AdvertiserID)

	if len(p.Fields) > 0 {
		rb.SetJSONArray("fields", p.Fields)
	}

	if len(p.ExcludeFieldTypesInResponse) > 0 {
		rb.SetJSONArray("exclude_field_types_in_response", p.ExcludeFieldTypesInResponse)
	}

	if p.Filtering != nil {
		filterJSON, err := json.Marshal(p.Filtering)
		if err != nil {
			return nil, ErrValidateFiltering
		}
		rb.SetParam("filtering", string(filterJSON))
	}

	if p.Page > 0 {
		rb.SetParam("page", strconv.Itoa(p.Page))
	} else {
		rb.SetParam("page", "1")
	}

	if p.PageSize > 0 {
		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
	} else {
		rb.SetParam("page_size", "100")
	}

	url := rb.String()
	return &url, nil
}

func (cs *AdgroupService) List(ctx context.Context, params *AdgroupParams) (*AdgroupListResponse, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)
	fmt.Println("------------- in --------------")

	var result AdgroupListResponse
	if err := cs.c.GetJSON(ctx, *urlStr, &result); err != nil {
		return nil, err
	}

	return &result, nil
}
