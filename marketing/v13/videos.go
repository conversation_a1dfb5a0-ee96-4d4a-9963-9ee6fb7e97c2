package v13

// import (
// 	"context"
// 	"errors"
// 	"fmt"
// 	"strconv"

// 	"github.com/dev-networldasia/tiktokservice"
// )

// const (
// 	ENDPOINT_TIKTOK_VIDEO = "/tt_video/list/"
// )

// var (
// 	ErrValidateItemTypes = errors.New("item_types is required")
// )

// type Video struct {
// 	AuthCode       string         `json:"auth_code"`
// 	ItemID         string         `json:"item_id"`
// 	Text           string         `json:"text"`
// 	Status         ItemStatus     `json:"status"`
// 	ItemType       ItemType       `json:"item_type"`
// 	CarouselInfo   *CarouselInfo  `json:"carousel_info,omitempty"`
// 	VideoInfo      *VideoInfo     `json:"video_info,omitempty"`
// 	AnchorList     []Anchor       `json:"anchor_list"`
// 	ProductRegions []string       `json:"product_regions"`
// 	URL            string         `json:"url"`
// 	SPUID          string         `json:"spu_id"`
// 	SPUName        string         `json:"spu_name"`
// 	StoreID        string         `json:"store_id"`
// 	UserInfo       *UserInfo      `json:"user_info"`
// 	AuthInfo       *Authorization `json:"auth_info"`
// }

// type UserInfo struct {
// 	TikTokName   string `json:"tiktok_name"`
// 	IdentityID   string `json:"identity_id"`
// 	IdentityType string `json:"identity_type"`
// }

// type Authorization struct {
// 	InviteStartTime string       `json:"invite_start_time"`
// 	AuthStartTime   string       `json:"auth_start_time"`
// 	AdAuthStatus    AdAuthStatus `json:"ad_auth_status"`
// 	AuthEndTime     string       `json:"auth_end_time"`
// }

// type Image struct {
// 	ImageURL    string `json:"image_url"`
// 	ImageHeight int    `json:"image_height"`
// 	ImageWidth  int    `json:"image_width"`
// }


// type TikTokVideoParams struct {
// 	AdvertiserID string   `json:"advertiser_id"`
// 	ItemTypes    []string `json:"item_types"`
// 	Keyword      string   `json:"keyword"`
// 	PageSize     int      `json:"page_size"`
// 	Page         int      `json:"page"`
// }

// type TikTokVideoResp struct {
// 	Code      int           `json:"code"`
// 	Message   string        `json:"message"`
// 	RequestID string        `json:"request_id"`
// 	Data      *TikTokVideos `json:"data"`
// }

// type TikTokVideos struct {
// 	List     []Video                `json:"list"`
// 	PageInfo tiktokservice.PageInfo `json:"page_info"`
// }

// type AdAuthStatus string

// const (
// 	AD_AUTH_STATUS_VALID   AdAuthStatus = "VALID"
// 	AD_AUTH_STATUS_EXPIRED AdAuthStatus = "EXPIRED"
// 	AD_AUTH_STATUS_REVOKED AdAuthStatus = "REVOKED"
// )

// func (p *TikTokVideoParams) validateBuildParams() (*string, error) {
// 	if p.AdvertiserID == "" {
// 		return nil, ErrValidateAdvertiserId
// 	}
// 	rb := tiktokservice.NewRoute(Version, ENDPOINT_TIKTOK_VIDEO).SetParam("advertiser_id", p.AdvertiserID)

// 	if len(p.ItemTypes) > 0 {
// 		rb.SetJSONArray("item_types", p.ItemTypes)
// 	}

// 	if p.Keyword != "" {
// 		rb.SetParam("keyword", p.Keyword)
// 	}

// 	if p.PageSize > 0 {
// 		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
// 	} else {
// 		rb.SetParam("page_size", "50")
// 	}

// 	if p.Page > 0 {
// 		rb.SetParam("page", strconv.Itoa(p.Page))
// 	} else {
// 		rb.SetParam("page", "1")
// 	}

// 	url := rb.String()
// 	return &url, nil
// }

// type TikTokVideosService struct {
// 	c *tiktokservice.Client
// }

// func (cs *TikTokVideosService) List(ctx context.Context, params *TikTokVideoParams) (*any, error) {
// 	urlStr, err := params.validateBuildParams()
// 	if err != nil {
// 		return nil, err
// 	}

// 	fmt.Println("url ", *urlStr)

// 	var videoResp any
// 	if err := cs.c.GetJSON(ctx, *urlStr, &videoResp); err != nil {
// 		return nil, err
// 	}

// 	return &videoResp, nil
// }
