package v13

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/dev-networldasia/tiktokservice"
)

const (
	ENDPOINT_TARGETING_REGION                    = "/search/region/"
	ENDPOINT_TARGETING_LANGUAGE                  = "/tool/language/"
	ENDPOINT_TARGETING_INTEREST_AND_BEHAVIORS    = "/targeting/search/"
	ENDPOINT_TARGETING_OS_VERIONS                = "/tool/os_version/"
	ENDPOINT_TARGETING_DEVICE_MODEL              = "/tool/device_model/"
	ENDPOINT_TARGETING_CARRIER                   = "/tool/carrier/"
	ENDPOINT_TARGETING_INTERNET_SERVICE_PROVIDER = "/tool/targeting/list/"
)

type TargetingService struct {
	c *tiktokservice.Client
}

/**
 * TARGETING - Region / Location
 */
type TargetgingRegionParam struct {
	AdvertiserID string `json:"advertiser_id"`
	Language     string `json:"language,omitempty"`
}

type TargetgingRegionResp struct {
	Code      int                   `json:"code"`
	Message   string                `json:"message"`
	Data      *TargetgingRegionData `json:"data"`
	RequestID string                `json:"request_id"`
}

type TargetgingRegionData struct {
	RegionList []Region `json:"region_list"`
}

type Region struct {
	RegionID       string      `json:"region_id"`
	RegionName     string      `json:"region_name"`
	RegionLevel    RegionLevel `json:"region_level"`
	CountryCode    string      `json:"country_code"`
	ParentID       *string     `json:"parent_id"`
	AreaType       AreaType    `json:"area_type"`
	SupportBelow18 bool        `json:"support_below_18"`
}

type RegionLevel string

const (
	REGION_LEVEL_COUNTRY  RegionLevel = "COUNTRY"
	REGION_LEVEL_PROVINCE RegionLevel = "PROVINCE"
	REGION_LEVEL_CITY     RegionLevel = "CITY"
	REGION_LEVEL_DISTRICT RegionLevel = "DISTRICT"
)

type AreaType string

const (
	AREA_TYPE_ADMIN               AreaType = "ADMIN"
	AREA_TYPE_METROPOLITAN_OR_DMA AreaType = "METROPOLITAN_OR_DMA"
)

func (p *TargetgingRegionParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}

	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_REGION).SetParam("advertiser_id", p.AdvertiserID)

	if p.Language != "" {
		rb.SetParam("language", p.Language)
	}

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingRegion(ctx context.Context, params *TargetgingRegionParam) (*TargetgingRegionResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var region TargetgingRegionResp
	if err := tis.c.GetJSON(ctx, *urlStr, &region); err != nil {
		return nil, err
	}
	return &region, nil
}

/**
 * TARGETING - Language
 */
type TargetingLanguageParam struct {
	AdvertiserID string `json:"advertiser_id"`
}

type TargetingLanguageResp struct {
	Code      int                    `json:"code"`
	Message   string                 `json:"message"`
	RequestID string                 `json:"request_id"`
	Data      *TargetingLanguageData `json:"data"`
}

type TargetingLanguageData struct {
	Languages []Language `json:"languages"`
}

type Language struct {
	Code string `json:"code"`
	Name string `json:"name"`
}

func (p *TargetingLanguageParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}

	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_LANGUAGE).SetParam("advertiser_id", p.AdvertiserID)

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingLanguage(ctx context.Context, params *TargetingLanguageParam) (*TargetingLanguageResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var languages TargetingLanguageResp
	if err := tis.c.GetJSON(ctx, *urlStr, &languages); err != nil {
		return nil, err
	}
	return &languages, nil
}

/**
 * TARGETING - Interests and behaviors
 */
type TargetingInterestsAndBehaviorsParam struct {
	AdvertiserID      string              `json:"advertiser_id"`
	TargetingType     TargetingType       `json:"targeting_type"`
	SubTargetingTypes []SubTargetingType  `json:"sub_targeting_types,omitempty"`
	SearchKeywords    []string            `json:"search_keywords,omitempty"`
	Language          string              `json:"language,omitempty"`
	Filtering         *TargetingFiltering `json:"filtering,omitempty"`
}

type TargetingFiltering struct {
	SpecialIndustries []SpecialIndustry `json:"special_industries"`
}

type TargetingType string

const (
	TARGETING_TYPE_INTEREST_AND_BEHAVIOR TargetingType = "INTEREST_AND_BEHAVIOR"
)

type SubTargetingType string

const (
	SUB_TARGETING_TYPE_GENERAL_INTEREST    SubTargetingType = "GENERAL_INTEREST"
	SUB_TARGETING_TYPE_ADDITIONAL_INTEREST SubTargetingType = "ADDITIONAL_INTEREST"
	SUB_TARGETING_TYPE_PURCHASE_INTENTION  SubTargetingType = "PURCHASE_INTENTION"
	SUB_TARGETING_TYPE_VIDEO_INTERACTION   SubTargetingType = "VIDEO_INTERACTION"
	SUB_TARGETING_TYPE_CREATOR_INTERACTION SubTargetingType = "CREATOR_INTERACTION"
	SUB_TARGETING_TYPE_HASHTAG_INTERACTION SubTargetingType = "HASHTAG_INTERACTION"
)

type SpecialIndustry string

const (
	SPECIAL_INDUSTRY_HOUSING    SpecialIndustry = "HOUSING"
	SPECIAL_INDUSTRY_EMPLOYMENT SpecialIndustry = "EMPLOYMENT"
	SPECIAL_INDUSTRY_CREDIT     SpecialIndustry = "CREDIT"
)

type TargetingInterestsAndBehaviorsResp struct {
	Code      int                                 `json:"code"`
	Message   string                              `json:"message"`
	RequestID string                              `json:"request_id"`
	Data      *TargetingInterestsAndBehaviorsData `json:"data"`
}

type TargetingInterestsAndBehaviorsData struct {
	GeneralInterest    GeneralInterest        `json:"general_interest"`
	AdditionalInterst  map[string]interface{} `json:"additional_interest"`
	PurchaseIntention  map[string]interface{} `json:"purchase_intention"`
	VideoInteraction   Interaction            `json:"video_interaction"`
	CreatorInteraction Interaction            `json:"creator_interaction"`
	HashtagInteraction Interaction            `json:"hashtag_interaction"`
}

type GeneralInterest struct {
	ListResult []GeneralInterestItem `json:"list_result"`
}

type Interaction struct {
	ListResult   []InteractionItem `json:"list_result"`
	SearchResult map[string]interface{}
}

type GeneralInterestItem struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Level       int      `json:"level"`
	ChildrenIds []string `json:"children_ids"`
}

type InteractionItem struct {
	ID                         string                       `json:"id"`
	Name                       string                       `json:"name"`
	SubTargetingType           SubTargetingType             `json:"sub_targeting_type"`
	SupportedSpecialIndustries []SupportedSpecialIndustries `json:"supported_special_industries"`
	Level                      int                          `json:"level"`
	ChildrenIDs                []string                     `json:"children_ids"`
	HashtagType                *HashtagType                 `json:"hashtag_type"`
}

type SupportedSpecialIndustries string

const (
	SUPPORTED_SPECIAL_INDUSTRIES_HOUSING    SupportedSpecialIndustries = "HOUSING"
	SUPPORTED_SPECIAL_INDUSTRIES_EMPLOYMENT SupportedSpecialIndustries = "EMPLOYMENT"
	SUPPORTED_SPECIAL_INDUSTRIES_CREDIT     SupportedSpecialIndustries = "CREDIT"
)

type HashtagType string

const (
	HASHTAG_TYPE_HASHTAG HashtagType = "HASHTAG"
)

func (p *TargetingInterestsAndBehaviorsParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_INTEREST_AND_BEHAVIORS).SetParam("advertiser_id", p.AdvertiserID)

	if p.TargetingType != "" {
		rb.SetParam("targeting_type", string(p.TargetingType))
	} else {
		rb.SetParam("targeting_type", "INTEREST_AND_BEHAVIOR")
	}

	if len(p.SubTargetingTypes) > 0 {
		subTargetingTypesStr := make([]string, len(p.SubTargetingTypes))
		for i, st := range p.SubTargetingTypes {
			subTargetingTypesStr[i] = string(st)
		}
		rb.SetJSONArray("sub_targeting_types", subTargetingTypesStr)
	}

	if len(p.SearchKeywords) > 0 {
		rb.SetJSONArray("search_keywords", p.SearchKeywords)
	}

	if p.Language != "" {
		rb.SetParam("language", p.Language)
	}

	if p.Filtering != nil {
		filterJSON, err := json.Marshal(p.Filtering)
		if err != nil {
			return nil, ErrValidateFiltering
		}
		rb.SetParam("filtering", string(filterJSON))
	}

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingInterestsAndBehaviors(ctx context.Context, params *TargetingInterestsAndBehaviorsParam) (*TargetingInterestsAndBehaviorsResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var interestAndBehavior TargetingInterestsAndBehaviorsResp
	if err := tis.c.GetJSON(ctx, *urlStr, &interestAndBehavior); err != nil {
		return nil, err
	}
	return &interestAndBehavior, nil
}

/**
 * TARGETING - OS versions
 */
type TargetingOsVersionsParạm struct {
	AdvertiserID string `json:"advertiser_id"`
	OsType       OsType `json:"os_type"`
}

type OsType string

const (
	OS_TYPE_ANDROID OsType = "ANDROID"
	OS_TYPE_IOS     OsType = "IOS"
)

type TargetingOsVersionsResp struct {
	Code      int                     `json:"code"`
	Message   string                  `json:"message"`
	RequestID string                  `json:"request_id"`
	Data      *TargetingOsVersionData `json:"data"`
}

type TargetingOsVersionData struct {
	OsVersions []OsVersion `json:"os_versions"`
}

type OsVersion struct {
	OsID    string `json:"os_id"`
	OsType  string `json:"os_type"`
	Version string `json:"version"`
	Name    string `json:"name"`
}

func (p *TargetingOsVersionsParạm) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_OS_VERIONS).SetParam("advertiser_id", p.AdvertiserID)

	if p.OsType != "" {
		rb.SetParam("os_type", string(p.OsType))
	}

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingOsVersions(ctx context.Context, params *TargetingOsVersionsParạm) (*TargetingOsVersionsResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var osVersion TargetingOsVersionsResp
	if err := tis.c.GetJSON(ctx, *urlStr, &osVersion); err != nil {
		return nil, err
	}
	return &osVersion, nil
}

/**
 * Targeting - Device model
 */
type TargetingDeviceModelParam struct {
	AdvertiserID string `json:"advertiser_id"`
}

type TargetingDeviceModelResp struct {
	Code      int                       `json:"code"`
	Message   string                    `json:"message"`
	RequestID string                    `json:"request_id"`
	Data      *TargetingDeviceModelData `json:"data"`
}

type TargetingDeviceModelData struct {
	DeviceModels []DeviceModel `json:"device_models"`
}

type DeviceModel struct {
	DeviceModelID   string   `json:"device_model_id"`
	DeviceModalName string   `json:"device_model_name"`
	ChildDeviceIDs  []string `json:"child_device_ids"`
	IsActive        bool     `json:"is_active"`
	Level           string   `json:"level"`
	OsType          OsType   `json:"os_type"`
}

func (p *TargetingDeviceModelParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_DEVICE_MODEL).SetParam("advertiser_id", p.AdvertiserID)

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingDeviceModel(ctx context.Context, params *TargetingDeviceModelParam) (*TargetingDeviceModelResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var deviceModel TargetingDeviceModelResp
	if err := tis.c.GetJSON(ctx, *urlStr, &deviceModel); err != nil {
		return nil, err
	}
	return &deviceModel, nil
}

/**
 * TARGETING - Carrier
 */
type TargetingCarrierParam struct {
	AdvertiserID string `json:"advertiser_id"`
}

type TargetingCarrierResp struct {
	Code      int                   `json:"code"`
	Message   string                `json:"message"`
	RequestID string                `json:"request_id"`
	Data      *TargetingCarrierData `json:"data"`
}

type TargetingCarrierData struct {
	Countries []Country `json:"countries"`
}

type Country struct {
	CountryCode string    `json:"country_code"`
	Carriers    []Carrier `json:"carriers"`
}

type Carrier struct {
	CarrierID string         `json:"carrier_id"`
	InUse     bool           `json:"in_use"`
	Name      string         `json:"name"`
	Value     []CarrierValue `json:"value"`
}

type CarrierValue struct {
	HNIID string `json:"hni_id"`
	InUse bool   `json:"in_use"`
}

func (p *TargetingCarrierParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_CARRIER).SetParam("advertiser_id", p.AdvertiserID)

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingCarrier(ctx context.Context, params *TargetingCarrierParam) (*TargetingCarrierResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var carriers TargetingCarrierResp
	if err := tis.c.GetJSON(ctx, *urlStr, &carriers); err != nil {
		return nil, err
	}
	return &carriers, nil
}

/**
 * TARGETING - Internet service providers
 */

type TargetingInternetServiceProviderParam struct {
	AdvertiserID string   `json:"advertiser_id"`
	LocationIDS  []string `json:"location_ids"`
	Scene        Scene    `json:"scene"`
}

type Scene string

const (
	SCENE_INTERNET_SERVICE_PROVIDER Scene = "ISP"
)

type TargetingInternetServiceProviderResp struct {
	Code      int                                   `json:"code"`
	Message   string                                `json:"message"`
	RequestID string                                `json:"request_id"`
	Data      *TargetingInternetServiceProviderData `json:"data"`
}

type TargetingInternetServiceProviderData struct {
	TargetingTagList []TargetingTag `json:"targeting_tag_list"`
	// ParentTags       any            `json:"parent_tags"`
}

type TargetingTag struct {
	TargetingType string                 `json:"targeting_type"`
	Name          string                 `json:"name"`
	StatusInfo    StatusInfo             `json:"status_info"`
	Isp           Isp                    `json:"isp"`
	Geo           map[string]interface{} `json:"geo"`
}

type StatusInfo struct {
	Status Status `json:"status"`
	Reason Reason `json:"reason"`
}

type Status string

const (
	STATUS_ENABLED  Status = "ENABLED"
	STATUS_DISABLED Status = "DISABLED"
)

type Reason string

const (
	REASON_OFFLINE       Reason = "OFFLINE"
	REASON_NOT_SUPPORTED Reason = "NOT_SUPPORTED"
)

type Isp struct {
	IspID      string `json:"isp_id"`
	LocationID string `json:"location_id"`
	RegionCode string `json:"region_code"`
	Name       string `json:"name"`
}

func (p *TargetingInternetServiceProviderParam) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_TARGETING_INTERNET_SERVICE_PROVIDER).SetParam("advertiser_id", p.AdvertiserID)

	if len(p.LocationIDS) > 0 {
		rb.SetJSONArray("location_ids", p.LocationIDS)
	}

	if p.Scene != "" {
		rb.SetParam("scene", string(p.Scene))
	} else {
		rb.SetParam("scene", "ISP")
	}

	url := rb.String()
	return &url, nil
}

func (tis *TargetingService) ListTargetingInternetServiceProvider(ctx context.Context, params *TargetingInternetServiceProviderParam) (*TargetingInternetServiceProviderResp, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var isp TargetingInternetServiceProviderResp
	if err := tis.c.GetJSON(ctx, *urlStr, &isp); err != nil {
		return nil, err
	}
	return &isp, nil
}
