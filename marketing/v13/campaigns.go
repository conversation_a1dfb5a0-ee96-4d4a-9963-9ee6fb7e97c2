package v13

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"

	"github.com/dev-networldasia/tiktokservice"
)

//https://business-api.tiktok.com/open_api/v1.3/campaign/get/?advertiser_id=7015148626926141442

const (
	ENDPOINT_CAMPAIGN_GET = "/campaign/get/"
)

var (
	ErrValidateAdvertiserId = errors.New("advertiser_id is required")
	ErrValidateFiltering    = errors.New("invalid filtering param")
)

type CampaignService struct {
	c *tiktokservice.Client
}

type Campaign struct {
	AdvertiserID                string   `json:"advertiser_id"`
	CampaignID                  string   `json:"campaign_id"`
	CampaignSystemOrigin        string   `json:"campaign_system_origin,omitempty"`
	CampaignName                string   `json:"campaign_name,omitempty"`
	CreateTime                  string   `json:"create_time,omitempty"`
	ModifyTime                  string   `json:"modify_time,omitempty"`
	ObjectiveType               string   `json:"objective_type,omitempty"`
	AppPromotionType            string   `json:"app_promotion_type,omitempty"`
	IsSearchCampaign            bool     `json:"is_search_campaign,omitempty"`
	IsSmartPerformanceCampaign  bool     `json:"is_smart_performance_campaign,omitempty"`
	CampaignType                string   `json:"campaign_type,omitempty"`
	AppID                       string   `json:"app_id,omitempty"`
	IsAdvancedDedicatedCampaign bool     `json:"is_advanced_dedicated_campaign,omitempty"`
	DisableSkanCampaign         bool     `json:"disable_skan_campaign,omitempty"`
	BidAlignType                string   `json:"bid_align_type,omitempty"`
	CampaignAppProfilePageState string   `json:"campaign_app_profile_page_state,omitempty"`
	RFCampaignType              string   `json:"rf_campaign_type,omitempty"`
	CampaignProductSource       string   `json:"campaign_product_source,omitempty"`
	CatalogEnabled              bool     `json:"catalog_enabled,omitempty"`
	SpecialIndustries           []string `json:"special_industries,omitempty"`
	BudgetOptimizeOn            bool     `json:"budget_optimize_on,omitempty"`
	BidType                     string   `json:"bid_type,omitempty"`
	DeepBidType                 string   `json:"deep_bid_type,omitempty"`
	RoasBid                     float64  `json:"roas_bid,omitempty"`
	OptimizationGoal            string   `json:"optimization_goal,omitempty"`
	BudgetMode                  string   `json:"budget_mode,omitempty"`
	Budget                      float64  `json:"budget,omitempty"`
	RtaID                       string   `json:"rta_id,omitempty"`
	RtaBidEnabled               bool     `json:"rta_bid_enabled,omitempty"`
	RtaProductSelectionEnabled  bool     `json:"rta_product_selection_enabled,omitempty"`
	OperationStatus             string   `json:"operation_status,omitempty"`
	SecondaryStatus             string   `json:"secondary_status,omitempty"`
	PostbackWindowMode          string   `json:"postback_window_mode,omitempty"`
	IsNewStructure              bool     `json:"is_new_structure,omitempty"`
	Objective                   string   `json:"objective,omitempty"`
}

type CampaignResp struct {
	AdvertiserID                string  `json:"advertiser_id"`
	CampaignID                  string  `json:"campaign_id"`
	CampaignName                string  `json:"campaign_name"`
	CampaignType                string  `json:"campaign_type"`
	CampaignProductSource       string  `json:"campaign_product_source"`
	DeepBidType                 *string `json:"deep_bid_type"`
	VirtualObjectiveType        *string `json:"virtual_objective_type"`
	ObjectiveType               string  `json:"objective_type"`
	BudgetMode                  string  `json:"budget_mode"`
	OperationStatus             string  `json:"operation_status"`
	Budget                      float64 `json:"budget"`
	IsAdvancedDedicatedCampaign bool    `json:"is_advanced_dedicated_campaign"`
	RtaID                       *string `json:"rta_id"`
	AppPromotionType            string  `json:"app_promotion_type"`
	CreateTime                  string  `json:"create_time"`
	ModifyTime                  string  `json:"modify_time"`
	RtaProductSelectionEnabled  bool    `json:"rta_product_selection_enabled"`
	SalesDestination            *string `json:"sales_destination"`
	IsSmartPerformanceCampaign  bool    `json:"is_smart_performance_campaign"`
	IsNewStructure              bool    `json:"is_new_structure"`
	DisableSkanCampaign         *string `json:"disable_skan_campaign"`
	CampaignAppProfilePageState string  `json:"campaign_app_profile_page_state"`
	Objective                   string  `json:"objective"`
	RtaBidEnabled               bool    `json:"rta_bid_enabled"`
	SecondaryStatus             string  `json:"secondary_status"`
	RoasBid                     float64 `json:"roas_bid"`
	IsSearchCampaign            bool    `json:"is_search_campaign"`
}

type ObjectiveType string

const (
	OBJECTIVE_TYPE_APP_PROMOTION   ObjectiveType = "APP_PROMOTION"
	OBJECTIVE_TYPE_WEB_CONVERSIONS ObjectiveType = "WEB_CONVERSIONS"
	OBJECTIVE_TYPE_REACH           ObjectiveType = "REACH"
	OBJECTIVE_TYPE_TRAFFIC         ObjectiveType = "TRAFFIC"
	OBJECTIVE_TYPE_VIDEO_VIEWS     ObjectiveType = "VIDEO_VIEWS"
	OBJECTIVE_TYPE_PRODUCT_SALES   ObjectiveType = "PRODUCT_SALES"
	OBJECTIVE_TYPE_ENGAGEMENT      ObjectiveType = "ENGAGEMENT"
	OBJECTIVE_TYPE_LEAD_GENERATION ObjectiveType = "LEAD_GENERATION"
	OBJECTIVE_TYPE_RF_REACH        ObjectiveType = "RF_REACH"
	OBJECTIVE_TYPE_TOPVIEW_REACH   ObjectiveType = "TOPVIEW_REACH"
)

/**
 * Get campaigns
 * Use this endpoint to get all campaigns for an ad account. Optionally, you can use filters in your request to return only certain campaigns.
 */
type CamppaignParams struct {
	AdvertiserID                string                 `json:"advertiser_id"`
	Fields                      []string               `json:"fields"`
	ExcludeFieldTypesInResponse []string               `json:"exclude_field_types_in_response"`
	Filtering                   map[string]interface{} `json:"filtering"`
	Page                        int                    `json:"page,omitempty"`
	PageSize                    int                    `json:"page_size,omitempty"`
}

func (p *CamppaignParams) validateBuildParams() (*string, error) {
	if p.AdvertiserID == "" {
		return nil, ErrValidateAdvertiserId
	}
	rb := tiktokservice.NewRoute(Version, ENDPOINT_CAMPAIGN_GET).SetParam("advertiser_id", p.AdvertiserID)

	if len(p.Fields) > 0 {
		rb.SetJSONArray("fields", p.Fields)
	}

	if len(p.ExcludeFieldTypesInResponse) > 0 {
		rb.SetJSONArray("exclude_field_types_in_response", p.ExcludeFieldTypesInResponse)
	}

	if len(p.Filtering) > 0 {
		filterJSON, err := json.Marshal(p.Filtering)
		if err != nil {
			return nil, ErrValidateFiltering
		}
		rb.SetParam("filtering", string(filterJSON))
	}

	if p.Page > 0 {
		rb.SetParam("page", strconv.Itoa(p.Page))
	} else {
		rb.SetParam("page", "1")
	}

	if p.PageSize > 0 {
		rb.SetParam("page_size", strconv.Itoa(p.PageSize))
	} else {
		rb.SetParam("page_size", "100")
	}

	url := rb.String()
	return &url, nil
}

type CampaignListResponse struct {
	Code      int          `json:"code"`
	Message   string       `json:"message"`
	RequestID string       `json:"request_id"`
	Data      CampaignData `json:"data"`
}

type CampaignData struct {
	List     []CampaignResp         `json:"list"`
	PageInfo tiktokservice.PageInfo `json:"page_info"`
}

func (cs *CampaignService) List(ctx context.Context, params *CamppaignParams) (*CampaignListResponse, error) {
	urlStr, err := params.validateBuildParams()
	if err != nil {
		return nil, err
	}

	fmt.Println("url ", *urlStr)

	var campResp CampaignListResponse
	if err := cs.c.GetJSON(ctx, *urlStr, &campResp); err != nil {
		return nil, err
	}

	return &campResp, nil
}

// --- GMV Max Campaigns ---
const ENDPOINT_GMV_MAX_CAMPAIGN_GET = "/gmv_max/campaign/get/"

type GMVMaxCampaignsService struct {
	c *tiktokservice.Client
}

type GMVMaxCampaignsFiltering struct {
	GMVMaxPromotionTypes []string `json:"gmv_max_promotion_types"`
	StoreIDs             []string `json:"store_ids,omitempty"`
	CampaignIDs          []string `json:"campaign_ids,omitempty"`
	CampaignName         string   `json:"campaign_name,omitempty"`
	PrimaryStatus        string   `json:"primary_status,omitempty"`
	CreationFilterStart  string   `json:"creation_filter_start_time,omitempty"`
	CreationFilterEnd    string   `json:"creation_filter_end_time,omitempty"`
}

type GMVMaxCampaignsParams struct {
	AdvertiserID string                   `json:"advertiser_id"`
	Fields       []string                 `json:"fields,omitempty"`
	Filtering    GMVMaxCampaignsFiltering `json:"filtering"`
	Page         int                      `json:"page,omitempty"`
	PageSize     int                      `json:"page_size,omitempty"`
}

type GMVMaxCampaign struct {
	AdvertiserID    string `json:"advertiser_id"`
	CampaignID      string `json:"campaign_id"`
	CampaignName    string `json:"campaign_name"`
	OperationStatus string `json:"operation_status"`
	CreateTime      string `json:"create_time"`
	ModifyTime      string `json:"modify_time"`
	ObjectiveType   string `json:"objective_type"`
	SecondaryStatus string `json:"secondary_status"`
}

type GMVMaxCampaignsListData struct {
	List     []GMVMaxCampaign       `json:"list"`
	PageInfo tiktokservice.PageInfo `json:"page_info"`
}

type GMVMaxCampaignsListResponse struct {
	Code      int                     `json:"code"`
	Message   string                  `json:"message"`
	RequestID string                  `json:"request_id"`
	Data      GMVMaxCampaignsListData `json:"data"`
}

func (s *GMVMaxCampaignsService) List(ctx context.Context, params *GMVMaxCampaignsParams) (*GMVMaxCampaignsListResponse, error) {
	rb := tiktokservice.NewRoute(Version, ENDPOINT_GMV_MAX_CAMPAIGN_GET).SetParam("advertiser_id", params.AdvertiserID)
	if len(params.Fields) > 0 {
		rb.SetJSONArray("fields", params.Fields)
	}
	if params.Page > 0 {
		rb.SetParam("page", fmt.Sprintf("%d", params.Page))
	}
	if params.PageSize > 0 {
		rb.SetParam("page_size", fmt.Sprintf("%d", params.PageSize))
	}
	if params.Filtering.GMVMaxPromotionTypes != nil || params.Filtering.StoreIDs != nil || params.Filtering.CampaignIDs != nil || params.Filtering.CampaignName != "" || params.Filtering.PrimaryStatus != "" || params.Filtering.CreationFilterStart != "" || params.Filtering.CreationFilterEnd != "" {
		filteringJSON, _ := json.Marshal(params.Filtering)
		rb.SetParam("filtering", string(filteringJSON))
	}
	url := rb.String()
	var resp GMVMaxCampaignsListResponse
	if err := s.c.GetJSON(ctx, url, &resp); err != nil {
		return nil, err
	}
	return &resp, nil
}
