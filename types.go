package tiktokservice

import (
	"encoding/json"
	"fmt"
	"time"
)

// ErrorContainer is a convenient type for embedding in other structs.
type ErrorContainer struct {
	Error *Error `json:"error"`
}

// GetError returns an error if available.
func (ec *ErrorContainer) GetError() error {
	if ec.Error != nil {
		return ec.Error
	}

	return nil
}

type ListResponse struct {
	Paging
	Data json.RawMessage `json:"data"`
}

type listResponse struct {
	Paging
	Data json.RawMessage `json:"data"`
}

// Error implements error.
type Error struct {
	Code      int
	Message   string
	RequestID string
}

// Error implements error.
func (e *Error) Error() string {
	return fmt.Sprintf("TikTok API error: code=%d, message=%s, request_id=%s", e.Code, e.Message, e.RequestID)
}

// TimeRange is the standard time range used by facebook.
type TimeRange struct {
	Since string `json:"since"`
	Until string `json:"until"`
}

type PageInfo struct {
	Page        int `json:"page"`
	PageSize    int `json:"page_size"`
	TotalPage   int `json:"total_page"`
	TotalNumber int `json:"total_number"`
}

// Paging is a convenient type for embedding in other structs.
type Paging struct {
	Paging PageInfo `json:"paging"`
}

// KeyValue represents a Facebook k/v entry in a API JSON response.
type KeyValue struct {
	ActionType string      `json:"action_type"`
	Value      json.Number `json:"value"`
}

// ID contains the ID field.
type ID struct {
	ID string `json:"id"`
}

// MetadataContainer contains a graph APIs object metadata.
type MetadataContainer struct {
	Metadata *Metadata `json:"metadata"`
}

// Metadata contains information about a graph API object.
type Metadata struct {
	Type        string            `json:"type"`
	Connections map[string]string `json:"connections"`
	Fields      []struct {
		Name        string `json:"name"`
		Description string `json:"description"`
		Type        string `json:"type,omitempty"`
	} `json:"fields"`
}

// MinimalResponse contains some information about a object being updated.
type MinimalResponse struct {
	ID          string    `json:"id"`
	Success     bool      `json:"success"`
	UpdatedTime time.Time `json:"updated_time"`
	ErrorContainer
}

// SummaryContainer contains a summary with a total count of items.
type SummaryContainer struct {
	Summary struct {
		TotalCount uint64 `json:"total_count"`
	} `json:"summary"`
}
